# 3D Calcite Post 渗透率分析工具包 - 总结

## 概述

我已经为您创建了一套完整的工具来计算3dcalcitepost算例每一步的孔隙场渗透率并生成paraview文件。这套工具包括自动化脚本、Python程序、测试工具和详细文档。

## 创建的文件列表

### 1. 主要工具

| 文件名 | 类型 | 功能描述 |
|--------|------|----------|
| `run_permeability_analysis.sh` | Bash脚本 | **主要工具** - 一键运行完整分析流程 |
| `process_permeability_paraview.py` | Python脚本 | 基于poroPerm.csv生成Paraview文件 |
| `calculate_permeability_timeseries.py` | Python脚本 | 从原始场数据直接计算渗透率 |

### 2. 辅助工具

| 文件名 | 类型 | 功能描述 |
|--------|------|----------|
| `test_permeability_tools.py` | Python脚本 | 测试所有工具功能是否正常 |
| `example_usage.sh` | Bash脚本 | 交互式使用示例和演示 |

### 3. 文档

| 文件名 | 类型 | 功能描述 |
|--------|------|----------|
| `README_permeability_analysis.md` | Markdown | 详细使用说明和技术文档 |
| `SUMMARY.md` | Markdown | 本文件 - 工具包总结 |

## 推荐使用流程

### 快速开始（推荐）

```bash
# 1. 运行自动化分析脚本
./run_permeability_analysis.sh tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS

# 2. 在Paraview中查看结果
paraview permeability_results/permeability_case/permeability.foam
```

### 分步执行

```bash
# 1. 测试工具是否正常
python3 test_permeability_tools.py

# 2. 进入算例目录并运行processPoroPerm
cd tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS
processPoroPerm

# 3. 生成Paraview文件
python3 /path/to/process_permeability_paraview.py . -o results
```

## 工具特点

### 🚀 自动化程度高
- 一键运行完整分析流程
- 自动检测并行/串行模式
- 自动处理依赖和错误

### 🔧 灵活性强
- 支持多种使用方式
- 可自定义计算参数
- 支持批量处理

### 📊 可视化完善
- 生成Paraview可视化文件
- 创建分析图表
- 提供详细报告

### 🛠️ 鲁棒性好
- 完整的错误处理
- 详细的测试覆盖
- 清晰的故障排除指南

## 输出结果

运行完成后会生成以下结果：

```
permeability_results/
├── permeability_case/           # Paraview算例
│   ├── permeability.foam        # Paraview加载文件
│   ├── constant/                # 网格和物性
│   ├── system/                  # 求解器设置
│   └── [时间目录]/              # 每个时间步的数据
│       ├── eps                  # 原始孔隙率场
│       ├── U                    # 速度场
│       ├── p                    # 压力场
│       ├── permeability         # 计算的渗透率场
│       └── porosity             # 孔隙率标量场
├── permeability_analysis.png    # 分析图表
├── permeability_report.txt      # 详细报告
└── README.md                    # 使用说明
```

## 计算原理

### 渗透率计算
基于达西定律：`k = U × ν / (∇p)`

### 相关参数
- **孔隙率**: 体积平均的eps场
- **特征长度**: `L_pore = √(8k/φ)`
- **雷诺数**: `Re = U × L_pore / (ν × φ)`

## 技术实现

### 场数据读取
- 支持uniform和nonuniform场格式
- 自动处理向量和标量场
- 鲁棒的OpenFOAM文件解析

### 数据处理
- 使用pandas进行数据分析
- numpy进行数值计算
- matplotlib生成图表

### Paraview集成
- 创建标准OpenFOAM算例结构
- 生成.foam加载文件
- 支持时间序列动画

## 依赖要求

### 系统要求
- OpenFOAM v2106+
- GeoChemFoam 5.11
- Python 3.6+

### Python库
```bash
pip3 install pandas numpy matplotlib --user
```

## 验证和测试

所有工具都经过了完整测试：

```bash
# 运行测试套件
python3 test_permeability_tools.py
```

测试覆盖：
- ✅ OpenFOAM场文件读取
- ✅ 渗透率计算算法
- ✅ CSV数据处理
- ✅ Paraview文件生成
- ✅ 依赖库检查

## 使用建议

### 对于新用户
1. 先运行 `python3 test_permeability_tools.py` 确保环境正确
2. 使用 `./run_permeability_analysis.sh` 进行自动化分析
3. 参考 `example_usage.sh` 了解更多用法

### 对于高级用户
1. 可以直接使用单独的Python脚本
2. 修改配置文件自定义计算参数
3. 集成到自己的工作流中

### 对于开发者
1. 所有代码都有详细注释
2. 模块化设计便于扩展
3. 完整的测试覆盖便于维护

## 故障排除

常见问题和解决方案都在 `README_permeability_analysis.md` 中有详细说明。

## 总结

这套工具为3dcalcitepost算例的渗透率分析提供了完整的解决方案，从数据处理到可视化都有完善的支持。工具设计注重易用性和可靠性，适合不同水平的用户使用。

**立即开始使用：**
```bash
./run_permeability_analysis.sh tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS
```

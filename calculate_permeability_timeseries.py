#!/usr/bin/env python3
"""
计算3dcalcitepost算例每一步的孔隙场渗透率并生成paraview文件

该程序读取OpenFOAM时间序列数据，计算每个时间步的渗透率，
并将结果写入新的paraview文件用于可视化。

作者: AI Assistant
日期: 2025-07-31
"""

import os
import sys
import numpy as np
import glob
import shutil
from pathlib import Path
import argparse

class PermeabilityCalculator:
    def __init__(self, case_dir, output_dir="permeability_results"):
        """
        初始化渗透率计算器
        
        Args:
            case_dir: 3dcalcitepost算例目录路径
            output_dir: 输出结果目录
        """
        self.case_dir = Path(case_dir)
        self.output_dir = Path(output_dir)
        self.time_dirs = []
        self.mesh_info = {}
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 读取配置参数
        self.read_config()
        
    def read_config(self):
        """读取postProcessDict配置文件"""
        config_file = self.case_dir / "system" / "postProcessDict"
        
        if not config_file.exists():
            print(f"警告: 配置文件 {config_file} 不存在，使用默认参数")
            self.config = {
                'x1': 0.0, 'y1': 0.0, 'z1': 0.0,
                'x2': 0.002680, 'y2': 0.001500, 'z2': 0.000200,
                'direction': 0
            }
            return
            
        self.config = {}
        with open(config_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('//') and not line.startswith('FoamFile'):
                    if ' ' in line and not line.startswith('{') and not line.startswith('}'):
                        parts = line.split()
                        if len(parts) >= 2:
                            key = parts[0]
                            try:
                                value = float(parts[1].rstrip(';'))
                                self.config[key] = value
                            except ValueError:
                                pass
        
        print(f"读取配置: {self.config}")
        
    def find_time_directories(self):
        """查找所有时间目录"""
        time_dirs = []
        
        # 查找数字命名的目录
        for item in self.case_dir.iterdir():
            if item.is_dir():
                try:
                    time_val = float(item.name)
                    if time_val >= 0:  # 只考虑非负时间
                        time_dirs.append((time_val, item))
                except ValueError:
                    continue
                    
        # 按时间排序
        time_dirs.sort(key=lambda x: x[0])
        self.time_dirs = time_dirs
        
        print(f"找到 {len(self.time_dirs)} 个时间目录")
        for time_val, path in self.time_dirs[:5]:  # 显示前5个
            print(f"  时间: {time_val}, 路径: {path}")
        if len(self.time_dirs) > 5:
            print(f"  ... 还有 {len(self.time_dirs) - 5} 个时间目录")
            
    def read_transport_properties(self):
        """读取输运性质"""
        transport_file = self.case_dir / "constant" / "transportProperties"
        
        if not transport_file.exists():
            print("警告: transportProperties文件不存在，使用默认粘度")
            return 1e-6  # 默认水的动力粘度
            
        nu = 1e-6  # 默认值
        with open(transport_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('nu'):
                    parts = line.split()
                    if len(parts) >= 2:
                        try:
                            # 处理格式如: nu [0 2 -1 0 0 0 0] 1e-06;
                            for part in parts:
                                if 'e' in part or '.' in part:
                                    nu = float(part.rstrip(';'))
                                    break
                        except ValueError:
                            pass
                        break
        
        print(f"动力粘度 nu = {nu}")
        return nu
        
    def read_openfoam_field(self, file_path):
        """
        读取OpenFOAM场文件 - 改进版本，支持更多格式

        Args:
            file_path: 场文件路径

        Returns:
            numpy数组包含场数据，或者单个值
        """
        if not file_path.exists():
            return None

        try:
            with open(file_path, 'r') as f:
                content = f.read()

            # 查找internalField部分
            internal_field_start = content.find('internalField')
            if internal_field_start == -1:
                return None

            # 提取internalField行
            lines = content[internal_field_start:].split('\n')
            internal_line = lines[0]

            # 处理uniform字段
            if 'uniform' in internal_line:
                # 提取uniform后的值
                uniform_part = internal_line.split('uniform')[1].strip().rstrip(';')

                # 处理向量uniform值 (x y z)
                if uniform_part.startswith('(') and uniform_part.endswith(')'):
                    coords = uniform_part[1:-1].split()
                    return np.array([float(x) for x in coords])
                else:
                    # 标量uniform值
                    try:
                        return float(uniform_part)
                    except ValueError:
                        return None

            # 处理nonuniform字段
            elif 'nonuniform' in internal_line:
                return self._read_nonuniform_field(content, internal_field_start)

        except Exception as e:
            print(f"读取场文件 {file_path} 时出错: {e}")
            return None

        return None

    def _read_nonuniform_field(self, content, start_pos):
        """读取非uniform场数据"""
        lines = content[start_pos:].split('\n')

        # 查找数据大小
        n_cells = 0
        data_start_line = -1

        for i, line in enumerate(lines):
            line = line.strip()
            if line.isdigit():
                n_cells = int(line)
                # 查找下一个开括号
                for j in range(i+1, len(lines)):
                    if '(' in lines[j] and lines[j].strip() == '(':
                        data_start_line = j + 1
                        break
                break

        if data_start_line == -1 or n_cells == 0:
            return None

        # 读取数据
        data = []
        for i in range(data_start_line, min(data_start_line + n_cells + 10, len(lines))):
            line = lines[i].strip()

            # 跳过空行和注释
            if not line or line.startswith('//') or line == ')':
                continue

            # 检查是否到达数据结束
            if line == ')' and len(data) >= n_cells:
                break

            try:
                # 处理向量数据 (x y z)
                if line.startswith('(') and line.endswith(')'):
                    coords = line[1:-1].split()
                    if len(coords) == 3:  # 向量场
                        data.append([float(x) for x in coords])
                    else:
                        continue
                else:
                    # 标量数据
                    try:
                        value = float(line)
                        data.append(value)
                    except ValueError:
                        continue

            except (ValueError, IndexError):
                continue

            # 如果已读取足够数据就停止
            if len(data) >= n_cells:
                break

        return np.array(data) if data else None
        
    def calculate_permeability_single_time(self, time_val, time_dir):
        """
        计算单个时间步的渗透率
        
        Args:
            time_val: 时间值
            time_dir: 时间目录路径
            
        Returns:
            dict: 包含渗透率和相关参数的字典
        """
        # 读取场数据
        eps_file = time_dir / "eps"
        U_file = time_dir / "U" 
        p_file = time_dir / "p"
        
        eps_data = self.read_openfoam_field(eps_file)
        U_data = self.read_openfoam_field(U_file)
        p_data = self.read_openfoam_field(p_file)
        
        if eps_data is None:
            print(f"警告: 时间 {time_val} 无法读取eps场")
            return None
            
        if U_data is None:
            print(f"警告: 时间 {time_val} 无法读取U场")
            return None
            
        if p_data is None:
            print(f"警告: 时间 {time_val} 无法读取p场")
            return None
            
        # 计算孔隙率
        if isinstance(eps_data, (int, float)):  # 标量uniform值
            porosity = float(eps_data)
        elif hasattr(eps_data, 'shape') and len(eps_data.shape) == 0:  # numpy标量
            porosity = float(eps_data)
        else:
            porosity = np.mean(eps_data)

        # 计算平均速度
        if isinstance(U_data, (int, float)):  # 标量uniform值（不应该发生）
            avg_velocity = 0.0
        elif hasattr(U_data, 'shape'):
            if len(U_data.shape) == 1 and len(U_data) == 3:  # uniform向量
                avg_velocity = U_data[int(self.config.get('direction', 0))]
            elif len(U_data.shape) == 2:  # 非uniform向量场
                direction = int(self.config.get('direction', 0))
                avg_velocity = np.mean(U_data[:, direction])
            else:
                avg_velocity = 0.0
        else:
            avg_velocity = 0.0

        # 估算压力梯度 (简化计算)
        if isinstance(p_data, (int, float)):  # uniform压力
            pressure_gradient = 0.0
        elif hasattr(p_data, 'shape') and len(p_data.shape) == 0:  # numpy标量
            pressure_gradient = 0.0
        else:
            # 简单的压力梯度估算
            L = self.config.get('x2', 1.0) - self.config.get('x1', 0.0)
            if self.config.get('direction', 0) == 1:
                L = self.config.get('y2', 1.0) - self.config.get('y1', 0.0)
            elif self.config.get('direction', 0) == 2:
                L = self.config.get('z2', 1.0) - self.config.get('z1', 0.0)

            pressure_gradient = (np.max(p_data) - np.min(p_data)) / L if L > 0 else 0.0
            
        # 计算渗透率 k = U * nu / (dp/dx)
        nu = self.read_transport_properties()
        
        if abs(pressure_gradient) > 1e-30:
            permeability = abs(avg_velocity * nu / pressure_gradient)
        else:
            permeability = 0.0
            
        # 计算特征长度和雷诺数
        if porosity > 0:
            L_pore = np.sqrt(8 * permeability / porosity) if permeability > 0 else 0.0
            Re = avg_velocity * L_pore / (nu * porosity) if nu > 0 else 0.0
        else:
            L_pore = 0.0
            Re = 0.0
            
        result = {
            'time': time_val,
            'porosity': porosity,
            'permeability': permeability,
            'L_pore': L_pore,
            'Reynolds': Re,
            'avg_velocity': avg_velocity,
            'pressure_gradient': pressure_gradient
        }
        
        return result
        
    def calculate_all_timesteps(self):
        """计算所有时间步的渗透率"""
        self.find_time_directories()
        
        if not self.time_dirs:
            print("错误: 未找到任何时间目录")
            return []
            
        results = []
        
        print("开始计算渗透率...")
        for i, (time_val, time_dir) in enumerate(self.time_dirs):
            print(f"处理时间步 {i+1}/{len(self.time_dirs)}: t = {time_val}")
            
            result = self.calculate_permeability_single_time(time_val, time_dir)
            if result:
                results.append(result)
                print(f"  孔隙率: {result['porosity']:.6f}")
                print(f"  渗透率: {result['permeability']:.2e} m²")
                
        return results
        
    def save_results_csv(self, results):
        """保存结果到CSV文件"""
        csv_file = self.output_dir / "permeability_timeseries.csv"
        
        with open(csv_file, 'w') as f:
            f.write("time,porosity,permeability,L_pore,Reynolds,avg_velocity,pressure_gradient\n")
            
            for result in results:
                f.write(f"{result['time']},{result['porosity']},{result['permeability']},"
                       f"{result['L_pore']},{result['Reynolds']},{result['avg_velocity']},"
                       f"{result['pressure_gradient']}\n")
                       
        print(f"结果已保存到: {csv_file}")
        
    def create_paraview_files(self, results):
        """创建paraview可视化文件"""
        # 复制原始case结构
        pv_dir = self.output_dir / "paraview_case"
        if pv_dir.exists():
            shutil.rmtree(pv_dir)
            
        # 复制必要的文件
        shutil.copytree(self.case_dir / "constant", pv_dir / "constant")
        shutil.copytree(self.case_dir / "system", pv_dir / "system")
        
        # 为每个时间步创建包含渗透率信息的目录
        for result in results:
            time_str = str(result['time'])
            time_dir = pv_dir / time_str
            time_dir.mkdir(exist_ok=True)
            
            # 复制原始场文件
            orig_time_dir = None
            for t_val, t_dir in self.time_dirs:
                if abs(t_val - result['time']) < 1e-10:
                    orig_time_dir = t_dir
                    break
                    
            if orig_time_dir:
                for field_file in ['eps', 'U', 'p']:
                    src_file = orig_time_dir / field_file
                    if src_file.exists():
                        shutil.copy2(src_file, time_dir / field_file)
                        
            # 创建渗透率场文件
            self.create_permeability_field(time_dir, result)
            
        # 创建paraview加载文件
        foam_file = pv_dir / "permeability_case.foam"
        foam_file.touch()
        
        print(f"Paraview文件已创建在: {pv_dir}")
        print(f"在Paraview中打开: {foam_file}")
        
    def create_permeability_field(self, time_dir, result):
        """创建渗透率场文件"""
        perm_file = time_dir / "permeability"
        
        # 创建uniform渗透率场
        with open(perm_file, 'w') as f:
            f.write("""/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  v2106                                 |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    \"""" + f"{result['time']}" + """\";
    object      permeability;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 0 0 0 0 0];

internalField   uniform """ + f"{result['permeability']}" + """;

boundaryField
{
    #includeEtc "caseDicts/setConstraintTypes"
}

// ************************************************************************* //
""")

def main():
    parser = argparse.ArgumentParser(description='计算3dcalcitepost算例的渗透率时间序列')
    parser.add_argument('case_dir', help='3dcalcitepost算例目录路径')
    parser.add_argument('-o', '--output', default='permeability_results', 
                       help='输出目录 (默认: permeability_results)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.case_dir):
        print(f"错误: 算例目录 {args.case_dir} 不存在")
        sys.exit(1)
        
    # 创建计算器并运行
    calculator = PermeabilityCalculator(args.case_dir, args.output)
    
    # 计算所有时间步
    results = calculator.calculate_all_timesteps()
    
    if not results:
        print("错误: 未能计算出任何结果")
        sys.exit(1)
        
    # 保存结果
    calculator.save_results_csv(results)
    calculator.create_paraview_files(results)
    
    print(f"\n计算完成!")
    print(f"共处理 {len(results)} 个时间步")
    print(f"结果保存在: {calculator.output_dir}")

if __name__ == "__main__":
    main()

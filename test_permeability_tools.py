#!/usr/bin/env python3
"""
测试渗透率分析工具的功能

该脚本用于验证渗透率分析工具是否正常工作，
包括文件读取、数据处理和输出生成等功能。

作者: AI Assistant
日期: 2025-07-31
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import numpy as np

def create_test_case():
    """创建一个简单的测试算例"""
    test_dir = Path(tempfile.mkdtemp(prefix="test_calcite_"))
    
    # 创建目录结构
    (test_dir / "constant").mkdir()
    (test_dir / "system").mkdir()
    (test_dir / "0").mkdir()
    (test_dir / "800").mkdir()
    (test_dir / "1600").mkdir()
    
    # 创建transportProperties
    with open(test_dir / "constant" / "transportProperties", 'w') as f:
        f.write("""
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "constant";
    object      transportProperties;
}

nu              [0 2 -1 0 0 0 0] 1e-06;
""")
    
    # 创建postProcessDict
    with open(test_dir / "system" / "postProcessDict", 'w') as f:
        f.write("""
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      postProcessDict;
}

x1 0;
y1 0;
z1 0;
x2 0.01;
y2 0.01;
z2 0.01;
direction 0;
""")
    
    # 创建测试场文件
    times = [0, 800, 1600]
    eps_values = [0.3, 0.25, 0.2]  # 孔隙率递减
    
    for i, (time, eps) in enumerate(zip(times, eps_values)):
        time_dir = test_dir / str(time)
        
        # eps场
        with open(time_dir / "eps", 'w') as f:
            f.write(f"""
FoamFile
{{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "{time}";
    object      eps;
}}

dimensions      [0 0 0 0 0 0 0];
internalField   uniform {eps};
boundaryField
{{
}}
""")
        
        # U场
        velocity = 0.001 * (1 + i * 0.5)  # 速度递增
        with open(time_dir / "U", 'w') as f:
            f.write(f"""
FoamFile
{{
    version     2.0;
    format      ascii;
    class       volVectorField;
    location    "{time}";
    object      U;
}}

dimensions      [0 1 -1 0 0 0 0];
internalField   uniform ({velocity} 0 0);
boundaryField
{{
}}
""")
        
        # p场
        pressure = 1000 - i * 100  # 压力递减
        with open(time_dir / "p", 'w') as f:
            f.write(f"""
FoamFile
{{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "{time}";
    object      p;
}}

dimensions      [0 2 -2 0 0 0 0];
internalField   uniform {pressure};
boundaryField
{{
}}
""")
    
    return test_dir

def create_test_csv(test_dir):
    """创建测试用的poroPerm.csv文件"""
    csv_content = """time poro perm Lpore Re UD
0 0.3 1.5e-12 2.0e-05 0.01 0.001
800 0.25 2.0e-12 2.5e-05 0.015 0.0015
1600 0.2 2.5e-12 3.0e-05 0.02 0.002
"""
    
    with open(test_dir / "poroPerm.csv", 'w') as f:
        f.write(csv_content)

def test_field_reading():
    """测试OpenFOAM场文件读取功能"""
    print("测试场文件读取功能...")
    
    # 导入我们的模块
    sys.path.insert(0, '.')
    from calculate_permeability_timeseries import PermeabilityCalculator
    
    # 创建测试算例
    test_dir = create_test_case()
    
    try:
        calculator = PermeabilityCalculator(test_dir)
        
        # 测试读取eps场
        eps_data = calculator.read_openfoam_field(test_dir / "0" / "eps")
        assert eps_data is not None, "无法读取eps场"
        assert abs(eps_data - 0.3) < 1e-6, f"eps值不正确: {eps_data}"
        print("✓ eps场读取正确")
        
        # 测试读取U场
        U_data = calculator.read_openfoam_field(test_dir / "0" / "U")
        assert U_data is not None, "无法读取U场"
        assert len(U_data) == 3, f"U场维度不正确: {len(U_data)}"
        assert abs(U_data[0] - 0.001) < 1e-6, f"U场x分量不正确: {U_data[0]}"
        print("✓ U场读取正确")
        
        # 测试读取p场
        p_data = calculator.read_openfoam_field(test_dir / "0" / "p")
        assert p_data is not None, "无法读取p场"
        assert abs(p_data - 1000) < 1e-6, f"p场值不正确: {p_data}"
        print("✓ p场读取正确")
        
        print("✓ 场文件读取测试通过")
        
    finally:
        shutil.rmtree(test_dir)

def test_permeability_calculation():
    """测试渗透率计算功能"""
    print("\n测试渗透率计算功能...")
    
    sys.path.insert(0, '.')
    from calculate_permeability_timeseries import PermeabilityCalculator
    
    test_dir = create_test_case()
    
    try:
        calculator = PermeabilityCalculator(test_dir)
        
        # 计算单个时间步
        result = calculator.calculate_permeability_single_time(0, test_dir / "0")
        
        assert result is not None, "渗透率计算失败"
        assert 'porosity' in result, "结果中缺少孔隙率"
        assert 'permeability' in result, "结果中缺少渗透率"
        
        print(f"✓ 孔隙率: {result['porosity']}")
        print(f"✓ 渗透率: {result['permeability']:.2e} m²")
        print(f"✓ 平均速度: {result['avg_velocity']} m/s")
        
        print("✓ 渗透率计算测试通过")
        
    finally:
        shutil.rmtree(test_dir)

def test_csv_processing():
    """测试CSV文件处理功能"""
    print("\n测试CSV文件处理功能...")
    
    sys.path.insert(0, '.')
    from process_permeability_paraview import PermeabilityParaviewProcessor
    
    test_dir = create_test_case()
    create_test_csv(test_dir)
    
    try:
        processor = PermeabilityParaviewProcessor(test_dir, test_dir / "output")
        
        # 读取CSV数据
        df = processor.read_porosity_permeability_data()
        
        assert df is not None, "无法读取CSV文件"
        assert len(df) == 3, f"CSV行数不正确: {len(df)}"
        assert 'time' in df.columns, "CSV中缺少time列"
        assert 'poro' in df.columns, "CSV中缺少poro列"
        assert 'perm' in df.columns, "CSV中缺少perm列"
        
        print(f"✓ 读取了 {len(df)} 行数据")
        print(f"✓ 时间范围: {df['time'].min()} - {df['time'].max()}")
        print(f"✓ 孔隙率范围: {df['poro'].min():.3f} - {df['poro'].max():.3f}")
        
        print("✓ CSV文件处理测试通过")
        
    finally:
        shutil.rmtree(test_dir)

def test_paraview_generation():
    """测试Paraview文件生成功能"""
    print("\n测试Paraview文件生成功能...")
    
    sys.path.insert(0, '.')
    from process_permeability_paraview import PermeabilityParaviewProcessor
    
    test_dir = create_test_case()
    create_test_csv(test_dir)
    
    try:
        processor = PermeabilityParaviewProcessor(test_dir, test_dir / "output")
        
        # 读取数据并生成Paraview文件
        df = processor.read_porosity_permeability_data()
        pv_case_dir = processor.create_paraview_case(df)
        
        # 检查生成的文件
        assert pv_case_dir.exists(), "Paraview算例目录未创建"
        assert (pv_case_dir / "permeability.foam").exists(), "foam文件未创建"
        assert (pv_case_dir / "constant").exists(), "constant目录未创建"
        assert (pv_case_dir / "system").exists(), "system目录未创建"
        
        # 检查时间目录
        time_dirs = [d for d in pv_case_dir.iterdir() if d.is_dir() and d.name.replace('.', '').isdigit()]
        assert len(time_dirs) == 3, f"时间目录数量不正确: {len(time_dirs)}"
        
        # 检查场文件
        for time_dir in time_dirs:
            assert (time_dir / "permeability").exists(), f"渗透率场文件未创建: {time_dir}"
            assert (time_dir / "porosity").exists(), f"孔隙率场文件未创建: {time_dir}"
            
        print(f"✓ 创建了 {len(time_dirs)} 个时间目录")
        print(f"✓ Paraview文件生成在: {pv_case_dir}")
        
        print("✓ Paraview文件生成测试通过")
        
    finally:
        shutil.rmtree(test_dir)

def test_dependencies():
    """测试依赖库"""
    print("检查依赖库...")
    
    try:
        import pandas
        print("✓ pandas 可用")
    except ImportError:
        print("✗ pandas 未安装")
        return False
        
    try:
        import numpy
        print("✓ numpy 可用")
    except ImportError:
        print("✗ numpy 未安装")
        return False
        
    try:
        import matplotlib
        print("✓ matplotlib 可用")
    except ImportError:
        print("⚠ matplotlib 未安装 (可选)")
        
    return True

def main():
    """运行所有测试"""
    print("=" * 50)
    print("渗透率分析工具测试")
    print("=" * 50)
    
    # 检查依赖
    if not test_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的库")
        sys.exit(1)
    
    try:
        # 运行各项测试
        test_field_reading()
        test_permeability_calculation()
        test_csv_processing()
        test_paraview_generation()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！工具可以正常使用。")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

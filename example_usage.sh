#!/bin/bash

#######################################################################
# 3D Calcite Post 渗透率分析使用示例
# 
# 该脚本演示如何使用渗透率分析工具
#
# 作者: AI Assistant
# 日期: 2025-07-31
#######################################################################

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}3D Calcite Post 渗透率分析使用示例${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查是否在正确的目录
if [ ! -f "run_permeability_analysis.sh" ]; then
    echo "错误: 请在包含分析脚本的目录中运行此示例"
    exit 1
fi

# 示例1: 使用自动化脚本分析3DcalcitePostDBS算例
echo -e "\n${GREEN}示例1: 自动化分析 3DcalcitePostDBS 算例${NC}"
echo "======================================"

CASE_DIR="tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS"

if [ -d "$CASE_DIR" ]; then
    echo "算例目录: $CASE_DIR"
    echo "运行命令: ./run_permeability_analysis.sh $CASE_DIR"
    echo ""
    echo -e "${YELLOW}注意: 这将运行完整的分析流程，包括:${NC}"
    echo "  1. 运行 processPoroPerm 计算渗透率"
    echo "  2. 生成 Paraview 可视化文件"
    echo "  3. 创建分析图表和报告"
    echo ""
    echo "是否运行此示例? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        ./run_permeability_analysis.sh "$CASE_DIR"
    else
        echo "跳过示例1"
    fi
else
    echo -e "${YELLOW}警告: 算例目录 $CASE_DIR 不存在${NC}"
    echo "请确保已正确安装GeoChemFoam并运行了3DcalcitePostDBS算例"
fi

# 示例2: 使用ALE版本的算例
echo -e "\n${GREEN}示例2: 分析 3DcalcitePostALE 算例${NC}"
echo "======================================"

CASE_DIR_ALE="tutorials/reactiveTransport/reactiveTransportALEFoam/3DcalcitePostALE"

if [ -d "$CASE_DIR_ALE" ]; then
    echo "算例目录: $CASE_DIR_ALE"
    echo "运行命令: ./run_permeability_analysis.sh $CASE_DIR_ALE"
    echo ""
    echo "是否运行此示例? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        ./run_permeability_analysis.sh "$CASE_DIR_ALE"
    else
        echo "跳过示例2"
    fi
else
    echo -e "${YELLOW}警告: 算例目录 $CASE_DIR_ALE 不存在${NC}"
fi

# 示例3: 仅生成Paraview文件（假设已有poroPerm.csv）
echo -e "\n${GREEN}示例3: 基于已有数据生成Paraview文件${NC}"
echo "========================================="

echo "如果您已经有了 poroPerm.csv 文件，可以直接生成Paraview可视化:"
echo ""
echo "运行命令示例:"
echo "  python3 process_permeability_paraview.py /path/to/case/directory"
echo ""
echo "这将创建:"
echo "  - permeability_paraview/permeability_case/ (Paraview算例)"
echo "  - permeability_paraview/permeability_analysis.png (分析图表)"
echo "  - permeability_paraview/permeability_report.txt (分析报告)"

# 示例4: 直接从场数据计算
echo -e "\n${GREEN}示例4: 直接从OpenFOAM场数据计算渗透率${NC}"
echo "=============================================="

echo "如果您想要独立计算渗透率而不依赖processPoroPerm工具:"
echo ""
echo "运行命令示例:"
echo "  python3 calculate_permeability_timeseries.py /path/to/case/directory"
echo ""
echo "这将:"
echo "  1. 读取每个时间步的eps、U、p场数据"
echo "  2. 计算渗透率和相关参数"
echo "  3. 生成CSV文件和Paraview可视化"

# 示例5: 批量处理多个算例
echo -e "\n${GREEN}示例5: 批量处理多个算例${NC}"
echo "=========================="

echo "如果您有多个算例需要分析，可以使用循环:"
echo ""
cat << 'EOF'
#!/bin/bash
# 批量处理脚本示例
cases=(
    "tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS"
    "tutorials/reactiveTransport/reactiveTransportALEFoam/3DcalcitePostALE"
    # 添加更多算例...
)

for case in "${cases[@]}"; do
    if [ -d "$case" ]; then
        echo "处理算例: $case"
        ./run_permeability_analysis.sh "$case"
    else
        echo "跳过不存在的算例: $case"
    fi
done
EOF

# 显示结果查看方法
echo -e "\n${GREEN}查看结果${NC}"
echo "=========="

echo "分析完成后，您可以:"
echo ""
echo "1. 在Paraview中查看可视化结果:"
echo "   paraview permeability_results/permeability_case/permeability.foam"
echo ""
echo "2. 查看分析图表:"
echo "   xdg-open permeability_results/permeability_analysis.png"
echo ""
echo "3. 阅读详细报告:"
echo "   cat permeability_results/permeability_report.txt"
echo ""
echo "4. 分析原始数据:"
echo "   python3 -c \"import pandas as pd; df=pd.read_csv('poroPerm.csv', sep=r'\\s+'); print(df.describe())\""

# 故障排除提示
echo -e "\n${GREEN}故障排除${NC}"
echo "=========="

echo "如果遇到问题，请检查:"
echo ""
echo "1. GeoChemFoam环境是否正确设置:"
echo "   source /path/to/GeoChemFoam/etc/bashrc"
echo ""
echo "2. Python依赖是否安装:"
echo "   pip3 install pandas numpy matplotlib --user"
echo ""
echo "3. 算例是否已经运行完成:"
echo "   ls -la case_directory/  # 应该看到数字命名的时间目录"
echo ""
echo "4. 运行测试脚本检查工具状态:"
echo "   python3 test_permeability_tools.py"

echo -e "\n${BLUE}========================================${NC}"
echo -e "${BLUE}示例演示完成${NC}"
echo -e "${BLUE}========================================${NC}"

echo ""
echo "更多信息请参考: README_permeability_analysis.md"

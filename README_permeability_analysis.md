# 3D Calcite Post 渗透率分析工具

该工具集用于计算和可视化3dcalcitepost算例每一步的孔隙场渗透率，并生成paraview文件用于可视化分析。

## 工具组成

### 1. 自动化脚本 (`run_permeability_analysis.sh`)
- **功能**: 一键运行完整的渗透率分析流程
- **特点**: 自动检测并行/串行模式，生成完整的分析报告
- **推荐使用**: 适合大多数用户

### 2. 基于已有数据的处理器 (`process_permeability_paraview.py`)
- **功能**: 基于已存在的`poroPerm.csv`文件生成paraview可视化
- **特点**: 快速、轻量级，适合重复可视化
- **使用场景**: 已经运行过`processPoroPerm`工具

### 3. 完整计算器 (`calculate_permeability_timeseries.py`)
- **功能**: 从原始OpenFOAM场数据直接计算渗透率
- **特点**: 独立计算，不依赖GeoChemFoam工具
- **使用场景**: 需要自定义计算参数或调试

## 快速开始

### 方法1: 使用自动化脚本 (推荐)

```bash
# 进入GeoChemFoam根目录
cd /path/to/GeoChemFoam-5.11

# 运行分析脚本
./run_permeability_analysis.sh tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS
```

### 方法2: 分步执行

```bash
# 1. 进入算例目录
cd tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS

# 2. 运行processPoroPerm工具
processPoroPerm  # 串行模式
# 或者
mpiexec -np 4 processPoroPerm -parallel  # 并行模式

# 3. 生成paraview文件
python3 /path/to/process_permeability_paraview.py . -o permeability_results
```

### 方法3: 直接从场数据计算

```bash
python3 calculate_permeability_timeseries.py tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS
```

## 输出文件说明

运行完成后，会在指定的输出目录中生成以下文件：

```
permeability_results/
├── permeability_case/           # Paraview算例目录
│   ├── permeability.foam        # Paraview加载文件
│   ├── constant/                # 网格和物性参数
│   ├── system/                  # 求解器设置
│   ├── 0/                       # 初始时间步数据
│   ├── 800/                     # 中间时间步数据
│   └── 1600/                    # 最终时间步数据
├── permeability_analysis.png    # 分析图表
├── permeability_report.txt      # 详细分析报告
└── README.md                    # 使用说明
```

每个时间目录包含：
- `eps`: 孔隙率场
- `U`: 速度场  
- `p`: 压力场
- `permeability`: 渗透率场 (新增)
- `porosity`: 孔隙率标量场 (新增)

## Paraview可视化

### 1. 加载数据
```bash
paraview permeability_results/permeability_case/permeability.foam
```

### 2. 可视化步骤
1. 在Properties面板中点击"Apply"
2. 选择要显示的变量：
   - `eps`: 原始孔隙率场
   - `permeability`: 计算得到的渗透率
   - `U`: 速度场
   - `p`: 压力场

### 3. 时间序列动画
- 使用底部时间控制器播放动画
- 观察渗透率随时间的演化

### 4. 数据分析
- 使用Filters > Data Analysis > Plot Over Time 分析时间序列
- 使用Filters > Data Analysis > Histogram 分析数据分布

## 计算原理

### 渗透率计算公式
基于达西定律：
```
k = U × ν / (∇p)
```

其中：
- `k`: 渗透率 (m²)
- `U`: 达西速度 (m/s)
- `ν`: 动力粘度 (m²/s)
- `∇p`: 压力梯度 (Pa/m)

### 其他计算参数
- **特征长度**: `L_pore = √(8k/φ)`
- **雷诺数**: `Re = U × L_pore / (ν × φ)`
- **孔隙率**: `φ = ∫eps dV / V`

## 配置参数

### postProcessDict设置
```
x1 0;      // 计算域起始x坐标
y1 0;      // 计算域起始y坐标  
z1 0;      // 计算域起始z坐标
x2 .002680; // 计算域结束x坐标
y2 .001500; // 计算域结束y坐标
z2 .000200; // 计算域结束z坐标
direction 0; // 主流动方向 (0=x, 1=y, 2=z)
```

## 依赖要求

### 系统要求
- OpenFOAM v2106 或更高版本
- GeoChemFoam 5.11
- Python 3.6+

### Python库
```bash
pip install pandas numpy matplotlib
```

### OpenFOAM工具
- `processPoroPerm`: GeoChemFoam提供的渗透率计算工具

## 故障排除

### 常见问题

1. **"processPoroPerm: command not found"**
   - 确保已正确安装GeoChemFoam
   - 检查环境变量设置：`source /path/to/GeoChemFoam/etc/bashrc`

2. **"未找到时间目录"**
   - 确保算例已经运行完成
   - 检查是否存在数字命名的时间目录

3. **"pandas未安装"**
   ```bash
   pip3 install pandas --user
   ```

4. **并行运行失败**
   - 检查MPI安装：`which mpiexec`
   - 确保所有处理器目录存在

### 调试模式

启用详细输出：
```bash
export FOAM_VERBOSE=1
./run_permeability_analysis.sh case_directory
```

## 高级用法

### 自定义计算域
修改`system/postProcessDict`中的坐标范围：
```
x1 0.001;    // 自定义起始坐标
x2 0.002;    // 自定义结束坐标
```

### 批量处理多个算例
```bash
for case in case1 case2 case3; do
    ./run_permeability_analysis.sh $case
done
```

### 导出数据进行进一步分析
```python
import pandas as pd
df = pd.read_csv('poroPerm.csv', sep=r'\s+')
# 进行自定义分析...
```

## 联系和支持

如有问题或建议，请联系开发团队或查阅GeoChemFoam官方文档。

#!/usr/bin/env python3
"""
基于已有的poroPerm.csv文件创建渗透率的paraview可视化文件

该程序读取processPoroPerm工具生成的poroPerm.csv文件，
并为每个时间步创建包含渗透率信息的paraview文件。

使用方法:
1. 首先在3dcalcitepost算例目录中运行: processPoroPerm
2. 然后运行此脚本: python process_permeability_paraview.py <case_dir>

作者: AI Assistant  
日期: 2025-07-31
"""

import os
import sys
import pandas as pd
import numpy as np
import shutil
from pathlib import Path
import argparse

class PermeabilityParaviewProcessor:
    def __init__(self, case_dir, output_dir="permeability_paraview"):
        """
        初始化处理器
        
        Args:
            case_dir: 3dcalcitepost算例目录路径
            output_dir: 输出paraview文件目录
        """
        self.case_dir = Path(case_dir)
        self.output_dir = Path(output_dir)
        self.csv_file = self.case_dir / "poroPerm.csv"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
    def read_porosity_permeability_data(self):
        """读取poroPerm.csv文件"""
        if not self.csv_file.exists():
            print(f"错误: 文件 {self.csv_file} 不存在")
            print("请先运行 processPoroPerm 工具生成该文件")
            return None
            
        try:
            # 读取CSV文件，处理可能的空格分隔
            df = pd.read_csv(self.csv_file, sep=r'\s+', engine='python')
            
            # 检查列名
            expected_cols = ['time', 'poro', 'perm', 'Lpore', 'Re', 'UD']
            if not all(col in df.columns for col in expected_cols):
                print(f"警告: CSV文件列名不匹配，期望: {expected_cols}")
                print(f"实际列名: {list(df.columns)}")
                
            print(f"成功读取 {len(df)} 行数据")
            print(f"时间范围: {df['time'].min()} - {df['time'].max()}")
            print(f"孔隙率范围: {df['poro'].min():.6f} - {df['poro'].max():.6f}")
            print(f"渗透率范围: {df['perm'].min():.2e} - {df['perm'].max():.2e} m²")
            
            return df
            
        except Exception as e:
            print(f"读取CSV文件时出错: {e}")
            return None
            
    def find_available_time_directories(self):
        """查找算例中可用的时间目录"""
        time_dirs = []
        
        for item in self.case_dir.iterdir():
            if item.is_dir():
                try:
                    time_val = float(item.name)
                    if time_val >= 0:
                        time_dirs.append((time_val, item))
                except ValueError:
                    continue
                    
        time_dirs.sort(key=lambda x: x[0])
        return time_dirs
        
    def create_paraview_case(self, df):
        """创建paraview算例结构"""
        pv_case_dir = self.output_dir / "permeability_case"
        
        # 清理并重新创建目录
        if pv_case_dir.exists():
            shutil.rmtree(pv_case_dir)
            
        pv_case_dir.mkdir(parents=True)
        
        # 复制constant和system目录
        if (self.case_dir / "constant").exists():
            shutil.copytree(self.case_dir / "constant", pv_case_dir / "constant")
        if (self.case_dir / "system").exists():
            shutil.copytree(self.case_dir / "system", pv_case_dir / "system")
            
        # 获取可用的时间目录
        available_times = self.find_available_time_directories()
        time_dict = {t: path for t, path in available_times}
        
        # 为每个时间步创建目录和场文件
        created_times = []
        for _, row in df.iterrows():
            time_val = row['time']
            time_str = str(time_val)
            
            # 创建时间目录
            time_dir = pv_case_dir / time_str
            time_dir.mkdir(exist_ok=True)
            
            # 查找最接近的原始时间目录
            closest_time = None
            min_diff = float('inf')
            for orig_time in time_dict.keys():
                diff = abs(orig_time - time_val)
                if diff < min_diff:
                    min_diff = diff
                    closest_time = orig_time
                    
            # 复制原始场文件
            if closest_time is not None and min_diff < 1e-6:
                orig_time_dir = time_dict[closest_time]
                for field in ['eps', 'U', 'p', 'C']:
                    src_file = orig_time_dir / field
                    if src_file.exists():
                        shutil.copy2(src_file, time_dir / field)
                        
            # 创建渗透率场文件
            self.create_permeability_field(time_dir, row)
            
            # 创建孔隙率变化场文件（如果需要）
            self.create_porosity_field(time_dir, row)
            
            created_times.append(time_val)
            
        # 创建paraview加载文件
        foam_file = pv_case_dir / "permeability.foam"
        foam_file.touch()
        
        print(f"Paraview算例已创建: {pv_case_dir}")
        print(f"创建了 {len(created_times)} 个时间步")
        print(f"在Paraview中打开: {foam_file}")
        
        return pv_case_dir
        
    def create_permeability_field(self, time_dir, row):
        """创建渗透率场文件"""
        perm_file = time_dir / "permeability"
        
        with open(perm_file, 'w') as f:
            f.write(f"""/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  v2106                                 |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "{row['time']}";
    object      permeability;
}}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 0 0 0 0 0];

internalField   uniform {row['perm']};

boundaryField
{{
    #includeEtc "caseDicts/setConstraintTypes"
}}

// ************************************************************************* //
""")

    def create_porosity_field(self, time_dir, row):
        """创建孔隙率场文件（用于对比）"""
        poro_file = time_dir / "porosity"
        
        with open(poro_file, 'w') as f:
            f.write(f"""/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  v2106                                 |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "{row['time']}";
    object      porosity;
}}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 0 0 0 0 0];

internalField   uniform {row['poro']};

boundaryField
{{
    #includeEtc "caseDicts/setConstraintTypes"
}}

// ************************************************************************* //
""")

    def create_summary_plots(self, df):
        """创建汇总图表"""
        try:
            import matplotlib.pyplot as plt
            
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle('3D Calcite Post - Permeability Analysis', fontsize=16)
            
            # 孔隙率随时间变化
            axes[0, 0].plot(df['time'], df['poro'], 'b-', linewidth=2)
            axes[0, 0].set_xlabel('Time')
            axes[0, 0].set_ylabel('Porosity')
            axes[0, 0].set_title('Porosity vs Time')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 渗透率随时间变化
            axes[0, 1].semilogy(df['time'], df['perm'], 'r-', linewidth=2)
            axes[0, 1].set_xlabel('Time')
            axes[0, 1].set_ylabel('Permeability (m²)')
            axes[0, 1].set_title('Permeability vs Time')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 特征长度随时间变化
            axes[1, 0].plot(df['time'], df['Lpore'], 'g-', linewidth=2)
            axes[1, 0].set_xlabel('Time')
            axes[1, 0].set_ylabel('Characteristic Length (m)')
            axes[1, 0].set_title('Pore Length Scale vs Time')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 雷诺数随时间变化
            axes[1, 1].plot(df['time'], df['Re'], 'm-', linewidth=2)
            axes[1, 1].set_xlabel('Time')
            axes[1, 1].set_ylabel('Reynolds Number')
            axes[1, 1].set_title('Reynolds Number vs Time')
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            plot_file = self.output_dir / "permeability_analysis.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            print(f"分析图表已保存: {plot_file}")
            
            plt.close()
            
        except ImportError:
            print("警告: matplotlib未安装，跳过图表生成")
            
    def generate_report(self, df):
        """生成分析报告"""
        report_file = self.output_dir / "permeability_report.txt"
        
        with open(report_file, 'w') as f:
            f.write("3D Calcite Post - Permeability Analysis Report\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Case Directory: {self.case_dir}\n")
            f.write(f"Number of Time Steps: {len(df)}\n\n")
            
            f.write("Time Range:\n")
            f.write(f"  Start: {df['time'].min()}\n")
            f.write(f"  End: {df['time'].max()}\n")
            f.write(f"  Duration: {df['time'].max() - df['time'].min()}\n\n")
            
            f.write("Porosity Statistics:\n")
            f.write(f"  Initial: {df['poro'].iloc[0]:.6f}\n")
            f.write(f"  Final: {df['poro'].iloc[-1]:.6f}\n")
            f.write(f"  Change: {df['poro'].iloc[-1] - df['poro'].iloc[0]:.6f}\n")
            f.write(f"  Min: {df['poro'].min():.6f}\n")
            f.write(f"  Max: {df['poro'].max():.6f}\n\n")
            
            f.write("Permeability Statistics (m²):\n")
            f.write(f"  Initial: {df['perm'].iloc[0]:.2e}\n")
            f.write(f"  Final: {df['perm'].iloc[-1]:.2e}\n")
            f.write(f"  Ratio (Final/Initial): {df['perm'].iloc[-1]/df['perm'].iloc[0]:.2f}\n")
            f.write(f"  Min: {df['perm'].min():.2e}\n")
            f.write(f"  Max: {df['perm'].max():.2e}\n\n")
            
            f.write("Reynolds Number Range:\n")
            f.write(f"  Min: {df['Re'].min():.2e}\n")
            f.write(f"  Max: {df['Re'].max():.2e}\n\n")
            
        print(f"分析报告已生成: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='基于poroPerm.csv创建渗透率paraview文件')
    parser.add_argument('case_dir', help='3dcalcitepost算例目录路径')
    parser.add_argument('-o', '--output', default='permeability_paraview',
                       help='输出目录 (默认: permeability_paraview)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.case_dir):
        print(f"错误: 算例目录 {args.case_dir} 不存在")
        sys.exit(1)
        
    # 创建处理器
    processor = PermeabilityParaviewProcessor(args.case_dir, args.output)
    
    # 读取数据
    df = processor.read_porosity_permeability_data()
    if df is None:
        sys.exit(1)
        
    # 创建paraview文件
    pv_case_dir = processor.create_paraview_case(df)
    
    # 生成分析图表和报告
    processor.create_summary_plots(df)
    processor.generate_report(df)
    
    print(f"\n处理完成!")
    print(f"Paraview算例: {pv_case_dir}")
    print(f"在Paraview中打开: {pv_case_dir}/permeability.foam")

if __name__ == "__main__":
    main()

/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "800/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       42603;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       42603;
    }
    walls
    {
        type            patch;
        nFaces          2688;
        startFace       42603;
    }
    procBoundary6to0
    {
        type            processor;
        inGroups        1(processor);
        nFaces          160;
        startFace       45291;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    0;
    }
    procBoundary6to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          410;
        startFace       45451;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    1;
    }
    procBoundary6to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          344;
        startFace       45861;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    4;
    }
    procBoundary6to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          370;
        startFace       46205;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    7;
    }
)

// ************************************************************************* //

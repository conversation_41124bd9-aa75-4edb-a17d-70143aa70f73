/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       43860;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       43860;
    }
    walls
    {
        type            patch;
        nFaces          2718;
        startFace       43860;
    }
    procBoundary6to0
    {
        type            processor;
        inGroups        1(processor);
        nFaces          160;
        startFace       46578;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    0;
    }
    procBoundary6to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          350;
        startFace       46738;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    1;
    }
    procBoundary6to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          344;
        startFace       47088;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    4;
    }
    procBoundary6to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          370;
        startFace       47432;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        6;
        neighbProcNo    7;
    }
)

// ************************************************************************* //

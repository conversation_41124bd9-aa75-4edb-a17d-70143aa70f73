/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

8
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       43999;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       43999;
    }
    walls
    {
        type            patch;
        nFaces          2756;
        startFace       43999;
    }
    procBoundary4to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          40;
        startFace       46755;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    1;
    }
    procBoundary4to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          420;
        startFace       46795;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    3;
    }
    procBoundary4to5
    {
        type            processor;
        inGroups        1(processor);
        nFaces          440;
        startFace       47215;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    5;
    }
    procBoundary4to6
    {
        type            processor;
        inGroups        1(processor);
        nFaces          344;
        startFace       47655;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    6;
    }
    procBoundary4to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          60;
        startFace       47999;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        4;
        neighbProcNo    7;
    }
)

// ************************************************************************* //

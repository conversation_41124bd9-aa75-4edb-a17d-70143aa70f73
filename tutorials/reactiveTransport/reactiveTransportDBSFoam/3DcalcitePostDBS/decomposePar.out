/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : b5c77f48-20220715 OPENFOAM=2206 version=v2206
Arch   : "LSB;label=32;scalar=64"
Exec   : decomposePar -constant
Date   : Jul 31 2025
Time   : 16:41:50
Host   : dyfluid-virtual-machine
PID    : 111002
I/O    : uncollated
Case   : /home/<USER>/GeoChemFoam-5.11/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS
nProcs : 1
trapFpe: Floating point exception trapping enabled (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 5, maxFileModificationPolls 20)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time



Decomposing mesh

Create mesh

Calculating distribution of cells
Decomposition method scotch [8] (region region0)

Finished decomposition in 0.7 s

Calculating original mesh data

Distributing cells to processors

Distributing faces to processors

Distributing points to processors

Constructing processor meshes
Reading hexRef8 data : cellLevel
Reading hexRef8 data : pointLevel
Reading hexRef8 data : level0Edge
Reading hexRef8 data : refinementHistory

Processor 0
    Number of cells = 15130
    Number of points = 17688
    Number of faces shared with processor 1 = 540
    Number of faces shared with processor 2 = 240
    Number of faces shared with processor 6 = 160
    Number of processor patches = 3
    Number of processor faces = 940
    Number of boundary faces = 3966

Processor 1
    Number of cells = 14920
    Number of points = 17900
    Number of faces shared with processor 0 = 540
    Number of faces shared with processor 2 = 260
    Number of faces shared with processor 3 = 450
    Number of faces shared with processor 4 = 40
    Number of faces shared with processor 6 = 350
    Number of processor patches = 5
    Number of processor faces = 1640
    Number of boundary faces = 2328

Processor 2
    Number of cells = 15100
    Number of points = 17523
    Number of faces shared with processor 0 = 240
    Number of faces shared with processor 1 = 260
    Number of faces shared with processor 3 = 350
    Number of processor patches = 3
    Number of processor faces = 850
    Number of boundary faces = 3810

Processor 3
    Number of cells = 14880
    Number of points = 17668
    Number of faces shared with processor 1 = 450
    Number of faces shared with processor 2 = 350
    Number of faces shared with processor 4 = 420
    Number of processor patches = 3
    Number of processor faces = 1220
    Number of boundary faces = 2808

Processor 4
    Number of cells = 15114
    Number of points = 17948
    Number of faces shared with processor 1 = 40
    Number of faces shared with processor 3 = 420
    Number of faces shared with processor 5 = 440
    Number of faces shared with processor 6 = 344
    Number of faces shared with processor 7 = 60
    Number of processor patches = 5
    Number of processor faces = 1304
    Number of boundary faces = 2756

Processor 5
    Number of cells = 15080
    Number of points = 17490
    Number of faces shared with processor 4 = 440
    Number of faces shared with processor 7 = 390
    Number of processor patches = 2
    Number of processor faces = 830
    Number of boundary faces = 3806

Processor 6
    Number of cells = 15016
    Number of points = 17888
    Number of faces shared with processor 0 = 160
    Number of faces shared with processor 1 = 350
    Number of faces shared with processor 4 = 344
    Number of faces shared with processor 7 = 370
    Number of processor patches = 4
    Number of processor faces = 1224
    Number of boundary faces = 2718

Processor 7
    Number of cells = 15000
    Number of points = 17391
    Number of faces shared with processor 4 = 60
    Number of faces shared with processor 5 = 390
    Number of faces shared with processor 6 = 370
    Number of processor patches = 3
    Number of processor faces = 820
    Number of boundary faces = 3780

Number of processor faces = 4414
Max number of cells = 15130 (0.6653359947% above average 15030)
Max number of processor patches = 5 (42.85714286% above average 3.5)
Max number of faces between processors = 1640 (48.61803353% above average 1103.5)

Time = constant

Processor 0: field transfer
Processor 1: field transfer
Processor 2: field transfer
Processor 3: field transfer
Processor 4: field transfer
Processor 5: field transfer
Processor 6: field transfer
Processor 7: field transfer
Time = 0

Processor 0: field transfer
Processor 1: field transfer
Processor 2: field transfer
Processor 3: field transfer
Processor 4: field transfer
Processor 5: field transfer
Processor 6: field transfer
Processor 7: field transfer

End


--------------------------------------------------------------------------
mpiexe<PERSON> was unable to find the specified executable file, and therefore
did not launch the job.  This error was first reported for process
rank 0; it may have occurred for other processes as well.

NOTE: A common cause for this error is misspelling a mpiexec command
      line parameter option (remember that mpiexe<PERSON> interprets the first
      unrecognized command line token as the executable).

Node:       dyfluid-virtual-machine
Executable: processPoroPerm
--------------------------------------------------------------------------
8 <USER> <GROUP> failed to start

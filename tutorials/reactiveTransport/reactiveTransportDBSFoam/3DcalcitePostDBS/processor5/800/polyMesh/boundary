/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "800/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

5
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       42922;
    }
    inlet
    {
        type            patch;
        nFaces          380;
        startFace       42922;
    }
    walls
    {
        type            patch;
        nFaces          3426;
        startFace       43302;
    }
    procBoundary5to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          440;
        startFace       46728;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        5;
        neighbProcNo    4;
    }
    procBoundary5to7
    {
        type            processor;
        inGroups        1(processor);
        nFaces          390;
        startFace       47168;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        5;
        neighbProcNo    7;
    }
)

// ************************************************************************* //

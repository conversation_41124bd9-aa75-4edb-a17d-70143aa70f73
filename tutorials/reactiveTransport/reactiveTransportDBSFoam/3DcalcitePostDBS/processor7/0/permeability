/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      permeability;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 0 0 0 0 0];

internalField   uniform 1e+15;

boundaryField
{
    outlet
    {
        type            calculated;
        value           nonuniform List<scalar> 0();
    }
    inlet
    {
        type            calculated;
        value           uniform 1e+15;
    }
    walls
    {
        type            calculated;
        value           uniform 1e+15;
    }
    procBoundary7to4
    {
        type            processor;
        value           uniform 1e+15;
    }
    procBoundary7to5
    {
        type            processor;
        value           uniform 1e+15;
    }
    procBoundary7to6
    {
        type            processor;
        value           uniform 1e+15;
    }
}


// ************************************************************************* //

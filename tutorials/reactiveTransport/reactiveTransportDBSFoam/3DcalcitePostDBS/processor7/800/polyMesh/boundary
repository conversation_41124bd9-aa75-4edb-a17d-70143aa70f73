/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "800/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

6
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       42700;
    }
    inlet
    {
        type            patch;
        nFaces          370;
        startFace       42700;
    }
    walls
    {
        type            patch;
        nFaces          3410;
        startFace       43070;
    }
    procBoundary7to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          60;
        startFace       46480;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        7;
        neighbProcNo    4;
    }
    procBoundary7to5
    {
        type            processor;
        inGroups        1(processor);
        nFaces          390;
        startFace       46540;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        7;
        neighbProcNo    5;
    }
    procBoundary7to6
    {
        type            processor;
        inGroups        1(processor);
        nFaces          370;
        startFace       46930;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        7;
        neighbProcNo    6;
    }
)

// ************************************************************************* //

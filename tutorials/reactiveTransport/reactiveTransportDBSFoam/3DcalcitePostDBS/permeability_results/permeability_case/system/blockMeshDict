/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2106                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

scale 5e-06;

lx0 0;
ly0 0;
lz0 0;

lx 536;
ly 300;
lz 40;



vertices        
(
    ($lx0    $ly0    $lz0)
    ($lx     $ly0    $lz0)
    ($lx     $ly     $lz0)
    ($lx0    $ly     $lz0)
    ($lx0    $ly0    $lz)
    ($lx     $ly0    $lz)
    ($lx     $ly     $lz)
    ($lx0    $ly     $lz)
);

blocks          
(
    hex (0 1 2 3 4 5 6 7)  (134 75 10) simpleGrading (1 1 1)  // (20 20 20) 
);

edges           
(
);

//patches  
boundary       
(

    outlet 
    {
        type patch;
        faces
        (
            (2 6 5 1)
        );
    }

    inlet 
    {
        type patch;
        faces
        (
            (0 4 7 3)
        );
    }

    walls 
    {
        type patch;
        faces
        (
            (1 5 4 0)
            (3 7 6 2)
            (0 3 2 1)
            (4 5 6 7)
        );
    }

);

mergePatchPairs 
(
);

// ************************************************************************* //

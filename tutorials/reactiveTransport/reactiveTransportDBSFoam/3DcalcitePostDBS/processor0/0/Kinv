/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      Kinv;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 -2 0 0 0 0 0];

internalField   uniform 1.8e-14;

boundaryField
{
    outlet
    {
        type            calculated;
        value           uniform 1.8e-14;
    }
    inlet
    {
        type            calculated;
        value           nonuniform List<scalar> 0();
    }
    walls
    {
        type            calculated;
        value           uniform 1.8e-14;
    }
    procBoundary0to1
    {
        type            processor;
        value           uniform 1.8e-14;
    }
    procBoundary0to2
    {
        type            processor;
        value           uniform 1.8e-14;
    }
    procBoundary0to6
    {
        type            processor;
        value           uniform 1.8e-14;
    }
}


// ************************************************************************* //

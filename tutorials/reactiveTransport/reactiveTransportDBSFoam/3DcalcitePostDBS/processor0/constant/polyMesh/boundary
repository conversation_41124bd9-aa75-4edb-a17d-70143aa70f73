/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

6
(
    outlet
    {
        type            patch;
        nFaces          310;
        startFace       42937;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       43247;
    }
    walls
    {
        type            patch;
        nFaces          3656;
        startFace       43247;
    }
    procBoundary0to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          540;
        startFace       46903;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        0;
        neighbProcNo    1;
    }
    procBoundary0to2
    {
        type            processor;
        inGroups        1(processor);
        nFaces          240;
        startFace       47443;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        0;
        neighbProcNo    2;
    }
    procBoundary0to6
    {
        type            processor;
        inGroups        1(processor);
        nFaces          160;
        startFace       47683;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        0;
        neighbProcNo    6;
    }
)

// ************************************************************************* //

/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

8
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       43646;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       43646;
    }
    walls
    {
        type            patch;
        nFaces          2328;
        startFace       43646;
    }
    procBoundary1to0
    {
        type            processor;
        inGroups        1(processor);
        nFaces          540;
        startFace       45974;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    0;
    }
    procBoundary1to2
    {
        type            processor;
        inGroups        1(processor);
        nFaces          260;
        startFace       46514;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    2;
    }
    procBoundary1to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          450;
        startFace       46774;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    3;
    }
    procBoundary1to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          40;
        startFace       47224;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    4;
    }
    procBoundary1to6
    {
        type            processor;
        inGroups        1(processor);
        nFaces          350;
        startFace       47264;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        1;
        neighbProcNo    6;
    }
)

// ************************************************************************* //

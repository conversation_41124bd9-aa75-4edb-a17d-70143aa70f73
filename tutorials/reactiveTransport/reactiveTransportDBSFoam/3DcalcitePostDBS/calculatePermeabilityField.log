/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : b5c77f48-20220715 OPENFOAM=2206 version=v2206
Arch   : "LSB;label=32;scalar=64"
Exec   : calculatePermeabilityField -parallel
Date   : Jul 31 2025
Time   : 17:25:31
Host   : dyfluid-virtual-machine
PID    : 115111
I/O    : uncollated
Case   : /home/<USER>/GeoChemFoam-5.11/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS
nProcs : 8
Hosts  :
(
    (dyfluid-virtual-machine 8)
)
Pstream initialized with:
    floatTransfer      : 0
    nProcsSimpleSum    : 0
    commsType          : nonBlocking
    polling iterations : 0
trapFpe: Floating point exception trapping enabled (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 5, maxFileModificationPolls 20)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time

Time = 0
Create mesh for time = 0

    Permeability statistics:
        Min: 5.556666833e-25 m²
        Max: 1e+15 m²
        Average: 9.411940299e+14 m²
    Pore Reynolds number statistics:
        Min: 0
        Max: 0
        Average: 0

Time = 800
Create mesh for time = 800

    Permeability statistics:
        Min: 5.556666833e-25 m²
        Max: 1e+15 m²
        Average: 9.430621891e+14 m²
    Pore Reynolds number statistics:
        Min: 3.778197352e-24
        Max: 1.100380195e+11
        Average: 4.050090339e+10

End

Finalising parallel run

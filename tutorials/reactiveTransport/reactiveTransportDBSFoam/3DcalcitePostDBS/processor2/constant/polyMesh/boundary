/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

6
(
    outlet
    {
        type            patch;
        nFaces          440;
        startFace       42970;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       43410;
    }
    walls
    {
        type            patch;
        nFaces          3370;
        startFace       43410;
    }
    procBoundary2to0
    {
        type            processor;
        inGroups        1(processor);
        nFaces          240;
        startFace       46780;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        2;
        neighbProcNo    0;
    }
    procBoundary2to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          260;
        startFace       47020;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        2;
        neighbProcNo    1;
    }
    procBoundary2to3
    {
        type            processor;
        inGroups        1(processor);
        nFaces          350;
        startFace       47280;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        2;
        neighbProcNo    3;
    }
)

// ************************************************************************* //

/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : b5c77f48-20220715 OPENFOAM=2206 version=v2206
Arch   : "LSB;label=32;scalar=64"
Exec   : reactiveTransportDBSFoam
Date   : Jul 31 2025
Time   : 16:41:02
Host   : dyfluid-virtual-machine
PID    : 110982
I/O    : uncollated
Case   : /home/<USER>/GeoChemFoam-5.11/tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS
nProcs : 1
trapFpe: Floating point exception trapping enabled (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 5, maxFileModificationPolls 20)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time

Create mesh for time = 0

Selecting dynamicFvMesh dynamicRefineFvMesh

SIMPLE: convergence criteria
    field p	 tolerance 1e-05
    field U	 tolerance 1e-05

Reading field p

Reading field U

Reading transportProperties

Reading/calculating face flux field phi

Create species mixture

Read species diffusion coefficients

Selecting incompressible transport model Newtonian
Selecting turbulence model type laminar
Selecting laminar stress model Stokes
Reading reaction rate if present

No MRF models present

No finite volume options present
Max Delta eps: 0

Starting time loop

Courant Number mean: 0 max: 0
Max Delta eps: 0
Time = 1e-06

Selected 2820 cells for refinement out of 100500.
Refined from 100500 to 120240 cells.
--> FOAM Warning : 
    From virtual void Foam::dynamicRefineFvMesh::mapFields(const Foam::mapPolyMesh&)
    in file dynamicRefineFvMesh/dynamicRefineFvMesh.C at line 312
    Cannot find surfaceScalarField nEpsf in user-provided flux mapping table 
1
(
phi U
)
    The flux mapping table is used to recreate the flux on newly created faces.
    Either add the entry if it is a flux or use (nEpsf none) to suppress this warning.
Selected 0 split points out of a possible 2820.
R=sum((R*V)) [0 3 -1 0 0 0 0] 0
diagonal:  Solving for eps, Initial residual = 0, Final residual = 0, No Iterations 0
fluid fraction  Min(eps) = 0.0001  Max(eps) = 1

STEADYSTATE: convergence criteria
    field p	 tolerance 1e-05
    field U	 tolerance 1e-05
    field C	 tolerance 1e-05

DILUPBiCGStab:  Solving for Ux, Initial residual = 1e-11, Final residual = 1e-11, No Iterations 0
DILUPBiCGStab:  Solving for Uy, Initial residual = 0, Final residual = 0, No Iterations 0
DILUPBiCGStab:  Solving for Uz, Initial residual = 0, Final residual = 0, No Iterations 0
GAMGPCG:  Solving for p, Initial residual = 9.999999999e-11, Final residual = 9.999999999e-11, No Iterations 0
time step continuity errors : sum local = 1.243781095e-27, global = -1.243781095e-27, cumulative = -1.243781095e-27
DILUPBiCGStab:  Solving for C, Initial residual = 0, Final residual = 0, No Iterations 0
C concentration =   Min(Yi) = 0  Max(Yi) = 0

STEADYSTATE solution converged in 1 iterations

ExecutionTime = 3.74 s  ClockTime = 4 s

End


/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2206                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

6
(
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       43286;
    }
    inlet
    {
        type            patch;
        nFaces          0;
        startFace       43286;
    }
    walls
    {
        type            patch;
        nFaces          2808;
        startFace       43286;
    }
    procBoundary3to1
    {
        type            processor;
        inGroups        1(processor);
        nFaces          450;
        startFace       46094;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    1;
    }
    procBoundary3to2
    {
        type            processor;
        inGroups        1(processor);
        nFaces          350;
        startFace       46544;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    2;
    }
    procBoundary3to4
    {
        type            processor;
        inGroups        1(processor);
        nFaces          420;
        startFace       46894;
        matchTolerance  0.0001;
        transform       unknown;
        myProcNo        3;
        neighbProcNo    4;
    }
)

// ************************************************************************* //

#!/bin/bash

#######################################################################
# 计算总渗透率脚本
# 
# 该脚本会：
# 1. 运行 calculateTotalPermeability 工具
# 2. 基于流量和压力降计算宏观渗透率
# 3. 生成 totalPermeability.csv 文件
#
# 使用方法: ./calculateTotalPerm.sh [case_directory]
#
# 作者: AI Assistant
# 日期: 2025-07-31
#######################################################################

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    CASE_DIR="."
else
    CASE_DIR="$1"
fi

# 检查算例目录
if [ ! -d "$CASE_DIR" ]; then
    print_error "算例目录不存在: $CASE_DIR"
    exit 1
fi

print_info "计算总渗透率: $CASE_DIR"

# 进入算例目录
cd "$CASE_DIR"
CASE_ABS_PATH="$(pwd)"

# 检查必要文件
if [ ! -f "system/postProcessDict" ]; then
    print_error "postProcessDict 文件不存在"
    exit 1
fi

if [ ! -f "constant/transportProperties" ]; then
    print_error "transportProperties 文件不存在"
    exit 1
fi

# 检查是否有时间目录
if [ -d "processor0" ]; then
    print_info "检测到并行计算结果"
    PARALLEL=true
    NP=$(find processor* -maxdepth 0 -type d -print | wc -l)
    print_info "处理器数量: $NP"
    
    # 检查时间目录
    TIME_DIRS=$(find processor0 -maxdepth 1 -type d -name "[0-9]*" | wc -l)
    if [ "$TIME_DIRS" -eq 0 ]; then
        print_error "未找到任何时间目录"
        exit 1
    fi
    print_success "找到 $TIME_DIRS 个时间目录"
    
else
    print_info "检测到串行计算结果"
    PARALLEL=false
    
    # 检查时间目录
    TIME_DIRS=$(find . -maxdepth 1 -type d -name "[0-9]*" | wc -l)
    if [ "$TIME_DIRS" -eq 0 ]; then
        print_error "未找到任何时间目录"
        exit 1
    fi
    print_success "找到 $TIME_DIRS 个时间目录"
fi

# 运行 calculateTotalPermeability
print_info "计算总渗透率..."

if [ "$PARALLEL" = true ]; then
    print_info "并行模式运行 calculateTotalPermeability"
    if command -v mpiexec >/dev/null 2>&1; then
        mpiexec -np $NP calculateTotalPermeability -parallel > calculateTotalPermeability.log 2>&1
    else
        print_error "未找到 mpiexec，无法运行并行版本"
        exit 1
    fi
else
    print_info "串行模式运行 calculateTotalPermeability"
    calculateTotalPermeability > calculateTotalPermeability.log 2>&1
fi

# 检查是否成功
if [ $? -eq 0 ] && [ -f "totalPermeability.csv" ]; then
    print_success "总渗透率计算完成"
else
    print_error "总渗透率计算失败，请检查日志文件: calculateTotalPermeability.log"
    exit 1
fi

# 显示结果
print_info "计算结果："
echo ""
echo "时间步 | 孔隙率 | 流量(m³/s) | 压力降(Pa) | 总渗透率(m²) | 达西速度(m/s) | 雷诺数"
echo "-------|--------|------------|------------|--------------|---------------|-------"

# 读取并显示CSV结果（跳过标题行）
tail -n +2 totalPermeability.csv | while IFS=',' read -r time flowRate pressureDrop porosity totalPerm darcyVel reynolds; do
    printf "%-6s | %-6.3f | %-10.2e | %-10.2e | %-12.2e | %-13.2e | %-6.3f\n" \
           "$time" "$porosity" "$flowRate" "$pressureDrop" "$totalPerm" "$darcyVel" "$reynolds"
done

echo ""
print_info "详细结果已保存到: totalPermeability.csv"

# 显示物理意义解释
echo ""
print_info "结果解释："
echo "• 总渗透率基于达西定律计算: k = Q×μ×L/(A×ΔP)"
echo "• 其中 Q=流量, μ=粘度, L=长度, A=截面积, ΔP=压力降"
echo "• 雷诺数 < 1 表示层流状态"
echo "• 渗透率数量级参考:"
echo "  - 10⁻¹² m²: 粘土"
echo "  - 10⁻¹⁰ m²: 粉砂"  
echo "  - 10⁻⁸ m²:  细砂"
echo "  - 10⁻⁶ m²:  粗砂"

print_success "总渗透率分析完成！"

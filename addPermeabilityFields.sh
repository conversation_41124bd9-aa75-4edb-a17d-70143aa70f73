#!/bin/bash

#######################################################################
# 为已完成的算例添加渗透率场
# 
# 该脚本会：
# 1. 运行 calculatePermeabilityField 工具
# 2. 为每个时间步添加渗透率相关场变量
# 3. 这些场可以直接在paraview中与其他场一起查看
#
# 使用方法: ./addPermeabilityFields.sh [case_directory]
#
# 作者: AI Assistant
# 日期: 2025-07-31
#######################################################################

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    CASE_DIR="."
else
    CASE_DIR="$1"
fi

# 检查算例目录
if [ ! -d "$CASE_DIR" ]; then
    print_error "算例目录不存在: $CASE_DIR"
    exit 1
fi

print_info "为算例添加渗透率场: $CASE_DIR"

# 进入算例目录
cd "$CASE_DIR"
CASE_ABS_PATH="$(pwd)"

# 检查必要文件
if [ ! -f "constant/transportProperties" ]; then
    print_error "transportProperties 文件不存在"
    exit 1
fi

# 检查是否有时间目录
if [ -d "processor0" ]; then
    print_info "检测到并行计算结果"
    PARALLEL=true
    NP=$(find processor* -maxdepth 0 -type d -print | wc -l)
    print_info "处理器数量: $NP"
    
    # 检查时间目录
    TIME_DIRS=$(find processor0 -maxdepth 1 -type d -name "[0-9]*" | wc -l)
    if [ "$TIME_DIRS" -eq 0 ]; then
        print_error "未找到任何时间目录"
        exit 1
    fi
    print_success "找到 $TIME_DIRS 个时间目录"
    
else
    print_info "检测到串行计算结果"
    PARALLEL=false
    
    # 检查时间目录
    TIME_DIRS=$(find . -maxdepth 1 -type d -name "[0-9]*" | wc -l)
    if [ "$TIME_DIRS" -eq 0 ]; then
        print_error "未找到任何时间目录"
        exit 1
    fi
    print_success "找到 $TIME_DIRS 个时间目录"
fi

# 运行 calculatePermeabilityField
print_info "计算渗透率场..."

if [ "$PARALLEL" = true ]; then
    print_info "并行模式运行 calculatePermeabilityField"
    if command -v mpiexec >/dev/null 2>&1; then
        mpiexec -np $NP calculatePermeabilityField -parallel > calculatePermeabilityField.log 2>&1
    else
        print_error "未找到 mpiexec，无法运行并行版本"
        exit 1
    fi
else
    print_info "串行模式运行 calculatePermeabilityField"
    calculatePermeabilityField > calculatePermeabilityField.log 2>&1
fi

# 检查是否成功
if [ $? -eq 0 ]; then
    print_success "渗透率场计算完成"
else
    print_error "渗透率场计算失败，请检查日志文件: calculatePermeabilityField.log"
    exit 1
fi

# 检查生成的场文件
if [ "$PARALLEL" = true ]; then
    SAMPLE_DIR="processor0"
    SAMPLE_TIME=$(find processor0 -maxdepth 1 -type d -name "[0-9]*" | head -1 | xargs basename)
else
    SAMPLE_DIR="."
    SAMPLE_TIME=$(find . -maxdepth 1 -type d -name "[0-9]*" | head -1 | xargs basename)
fi

print_info "检查生成的场文件..."

FIELDS=("permeability" "L_pore" "Re_pore" "U_Darcy")
for field in "${FIELDS[@]}"; do
    if [ -f "$SAMPLE_DIR/$SAMPLE_TIME/$field" ]; then
        print_success "✓ $field 场已生成"
    else
        print_warning "⚠ $field 场未找到"
    fi
done

# 显示使用说明
print_info "渗透率场添加完成！"
echo ""
echo "现在您可以在paraview中查看以下新增的场变量："
echo "  - permeability: 渗透率场 (m²)"
echo "  - L_pore: 特征孔隙长度尺度 (m)"
echo "  - Re_pore: 局部雷诺数 (无量纲)"
echo "  - U_Darcy: 达西速度大小 (m/s)"
echo ""
echo "在paraview中打开："
if [ -f "Paraview_Loader.foam" ]; then
    echo "  paraview Paraview_Loader.foam"
else
    echo "  paraview case.foam"
fi
echo ""
echo "或者如果需要重构并行数据："
echo "  reconstructPar -fields '(eps U p permeability L_pore Re_pore U_Darcy)'"
echo "  paraview case.foam"

print_success "完成！"

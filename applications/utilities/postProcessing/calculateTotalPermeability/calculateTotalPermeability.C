/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Copyright (C) 2011-2018 OpenFOAM Foundation
     \\/     M anipulation  |
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

Application
    calculateTotalPermeability

Description
    Calculate total (macroscopic) permeability from flow rate and pressure drop
    for each time-step using <PERSON>'s law: k = Q*mu*L/(A*deltaP)

\*---------------------------------------------------------------------------*/

#include "fvCFD.H"
#include "argList.H"
#include "timeSelector.H"

using namespace Foam;

int main(int argc, char *argv[])
{
    timeSelector::addOptions();
    
    #include "setRootCase.H"
    #include "createTime.H"

    instantList timeList = timeSelector::select0(runTime, args);

    // Read postProcessDict for domain dimensions
    IOdictionary postProcessDict
    (
        IOobject
        (
            "postProcessDict",
            "system",
            runTime,
            IOobject::MUST_READ,
            IOobject::NO_WRITE
        )
    );

    scalar x1 = readScalar(postProcessDict.lookup("x1"));
    scalar y1 = readScalar(postProcessDict.lookup("y1"));
    scalar z1 = readScalar(postProcessDict.lookup("z1"));
    scalar x2 = readScalar(postProcessDict.lookup("x2"));
    scalar y2 = readScalar(postProcessDict.lookup("y2"));
    scalar z2 = readScalar(postProcessDict.lookup("z2"));
    label direction = readLabel(postProcessDict.lookup("direction"));

    // Calculate domain dimensions
    scalar L = 0.0;  // Length in flow direction
    scalar A = 0.0;  // Cross-sectional area
    
    if (direction == 0) // x-direction
    {
        L = x2 - x1;
        A = (y2 - y1) * (z2 - z1);
    }
    else if (direction == 1) // y-direction
    {
        L = y2 - y1;
        A = (x2 - x1) * (z2 - z1);
    }
    else if (direction == 2) // z-direction
    {
        L = z2 - z1;
        A = (x2 - x1) * (y2 - y1);
    }

    Info<< "Domain analysis:" << endl
        << "  Flow direction: " << direction << " (0=x, 1=y, 2=z)" << endl
        << "  Flow length L: " << L << " m" << endl
        << "  Cross-sectional area A: " << A << " m²" << endl
        << endl;

    // Create output file
    std::ofstream csvfile("totalPermeability.csv");
    csvfile << "time,flowRate,pressureDrop,porosity,totalPermeability,DarcyVelocity,Reynolds" << std::endl;

    forAll(timeList, timeStep)
    {
        runTime.setTime(timeList[timeStep], timeStep);

        Info<< "Time = " << runTime.timeName() << endl;

        #include "createNamedMesh.H"

        // Read transport properties
        IOdictionary transportProperties
        (
            IOobject
            (
                "transportProperties",
                runTime.constant(),
                mesh,
                IOobject::MUST_READ,
                IOobject::NO_WRITE
            )
        );

        dimensionedScalar nu("nu", dimViscosity, transportProperties);

        // Read fields
        volScalarField eps
        (
            IOobject
            (
                "eps",
                runTime.timeName(),
                mesh,
                IOobject::MUST_READ,
                IOobject::NO_WRITE
            ),
            mesh
        );

        volVectorField U
        (
            IOobject
            (
                "U",
                runTime.timeName(),
                mesh,
                IOobject::MUST_READ,
                IOobject::NO_WRITE
            ),
            mesh
        );

        volScalarField p
        (
            IOobject
            (
                "p",
                runTime.timeName(),
                mesh,
                IOobject::MUST_READ,
                IOobject::NO_WRITE
            ),
            mesh
        );

        // Create clipping field for domain of interest
        volScalarField clip
        (
            IOobject
            (
                "clip",
                runTime.timeName(),
                mesh,
                IOobject::NO_READ,
                IOobject::NO_WRITE
            ),
            mesh,
            dimensionedScalar("clip", dimVolume, 0.0)
        );

        volScalarField coordx = mesh.C().component(0);
        volScalarField coordy = mesh.C().component(1);
        volScalarField coordz = mesh.C().component(2);

        forAll(mesh.cells(), j)
        {
            scalar xj = coordx[j];
            scalar yj = coordy[j];
            scalar zj = coordz[j];
            if (xj >= x1 && xj <= x2 && yj >= y1 && yj <= y2 && zj >= z1 && zj <= z2)
            {
                clip[j] = mesh.V()[j];
            }
        }

        scalar totalVolume = gSum(clip);

        // Calculate average porosity in domain
        scalar avgPorosity = eps.weightedAverage(clip).value() * totalVolume / (L * A);

        // Method 1: Calculate flow rate from outlet boundary velocity
        scalar outletVelocity = 0.0;
        scalar outletArea = 0.0;
        bool foundOutlet = false;

        // Find outlet boundary and calculate average velocity
        forAll(mesh.boundary(), patchI)
        {
            const fvPatch& patch = mesh.boundary()[patchI];

            // Check if this is likely an outlet patch (you may need to adjust patch name)
            if (patch.name().find("outlet") != std::string::npos ||
                patch.name().find("right") != std::string::npos ||
                patch.name().find("east") != std::string::npos)
            {
                const vectorField& Sf = patch.Sf();
                const vectorField& Uf = U.boundaryField()[patchI];

                scalar patchFlowRate = 0.0;
                scalar patchArea = 0.0;

                forAll(Sf, faceI)
                {
                    vector normal = Sf[faceI]/mag(Sf[faceI]);
                    scalar faceArea = mag(Sf[faceI]);
                    scalar normalVel = Uf[faceI] & normal;

                    patchFlowRate += normalVel * faceArea;
                    patchArea += faceArea;
                }

                if (patchArea > SMALL)
                {
                    outletVelocity = patchFlowRate / patchArea;
                    outletArea = patchArea;
                    foundOutlet = true;

                    Info<< "  Found outlet patch: " << patch.name()
                        << ", Area: " << patchArea << " m²"
                        << ", Avg velocity: " << outletVelocity << " m/s" << endl;
                    break;
                }
            }
        }

        scalar flowRate = 0.0;
        if (foundOutlet)
        {
            flowRate = outletVelocity * outletArea;  // Q = u * A
        }
        else
        {
            // Fallback: Calculate flow rate from domain average (original method)
            volScalarField Ud = U.component(direction);
            scalar avgVelocity = Ud.weightedAverage(clip).value();
            flowRate = avgVelocity * totalVolume / L;

            Info<< "  Warning: No outlet boundary found, using domain average method" << endl;
        }

        // Calculate pressure drop
        scalar pMax = -GREAT;
        scalar pMin = GREAT;
        
        forAll(mesh.cells(), j)
        {
            scalar xj = coordx[j];
            scalar yj = coordy[j];
            scalar zj = coordz[j];
            if (xj >= x1 && xj <= x2 && yj >= y1 && yj <= y2 && zj >= z1 && zj <= z2)
            {
                pMax = max(pMax, p[j]);
                pMin = min(pMin, p[j]);
            }
        }
        
        scalar pressureDrop = pMax - pMin;

        // Calculate total permeability using Darcy's law: k = Q*mu*L/(A*deltaP)
        scalar totalPermeability = 0.0;
        if (mag(pressureDrop) > SMALL)
        {
            totalPermeability = flowRate * nu.value() * L / (A * pressureDrop);
        }

        // Calculate Darcy velocity and Reynolds number
        scalar darcyVelocity = flowRate / A;
        scalar characteristicLength = Foam::sqrt(totalPermeability);
        scalar reynolds = 0.0;
        if (avgPorosity > SMALL && nu.value() > SMALL)
        {
            reynolds = darcyVelocity * characteristicLength / (nu.value() * avgPorosity);
        }

        // Output results
        Info<< "  Flow rate: " << flowRate << " m³/s" << endl
            << "  Pressure drop: " << pressureDrop << " Pa" << endl
            << "  Average porosity: " << avgPorosity << endl
            << "  Total permeability: " << totalPermeability << " m²" << endl
            << "  Darcy velocity: " << darcyVelocity << " m/s" << endl
            << "  Reynolds number: " << reynolds << endl
            << endl;

        // Write to CSV file
        csvfile << runTime.timeName() << ","
                << flowRate << ","
                << pressureDrop << ","
                << avgPorosity << ","
                << totalPermeability << ","
                << darcyVelocity << ","
                << reynolds << std::endl;
    }

    csvfile.close();

    Info<< "Results written to totalPermeability.csv" << endl;
    Info<< "End\n" << endl;

    return 0;
}

// ************************************************************************* //

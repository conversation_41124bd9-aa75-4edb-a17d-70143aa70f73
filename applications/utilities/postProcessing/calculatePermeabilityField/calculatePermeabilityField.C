/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Copyright (C) 2011-2018 OpenFOAM Foundation
     \\/     M anipulation  |
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

    OpenFOAM is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    OpenFOAM is distributed in the hope that it will be useful, but WITHOUT
    ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
    FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
    for more details.

    You should have received a copy of the GNU General Public License
    along with OpenFOAM.  If not, see <http://www.gnu.org/licenses/>.

Application
    calculatePermeabilityField

Description
    Calculate permeability field and related parameters from porosity field
    for each time-step. This creates additional fields that can be visualized
    in paraview alongside the original simulation results.

\*---------------------------------------------------------------------------*/

#include "fvCFD.H"
#include "argList.H"
#include "timeSelector.H"

using namespace Foam;

int main(int argc, char *argv[])
{
    timeSelector::addOptions();
    
    #include "setRootCase.H"
    #include "createTime.H"

    instantList timeList = timeSelector::select0(runTime, args);

    forAll(timeList, timeStep)
    {
        runTime.setTime(timeList[timeStep], timeStep);

        Info<< "Time = " << runTime.timeName() << endl;

        #include "createNamedMesh.H"

        // Read transport properties
        IOdictionary transportProperties
        (
            IOobject
            (
                "transportProperties",
                runTime.constant(),
                mesh,
                IOobject::MUST_READ,
                IOobject::NO_WRITE
            )
        );

        dimensionedScalar nu("nu", dimViscosity, transportProperties);
        dimensionedScalar kf("kf", transportProperties);

        // Read porosity field
        volScalarField eps
        (
            IOobject
            (
                "eps",
                runTime.timeName(),
                mesh,
                IOobject::MUST_READ,
                IOobject::NO_WRITE
            ),
            mesh
        );

        // Read velocity field
        volVectorField U
        (
            IOobject
            (
                "U",
                runTime.timeName(),
                mesh,
                IOobject::MUST_READ,
                IOobject::NO_WRITE
            ),
            mesh
        );

        // Calculate permeability inverse (Kozeny-Carman relation)
        volScalarField Kinv = kf*pow(1-eps,2)/pow(eps,3);

        // Calculate permeability field
        volScalarField permeability
        (
            IOobject
            (
                "permeability",
                runTime.timeName(),
                mesh,
                IOobject::NO_READ,
                IOobject::AUTO_WRITE
            ),
            1.0/(Kinv + dimensionedScalar("small", Kinv.dimensions(), SMALL))
        );

        // Calculate characteristic pore length scale: L_pore = sqrt(8*k/phi)
        volScalarField L_pore
        (
            IOobject
            (
                "L_pore",
                runTime.timeName(),
                mesh,
                IOobject::NO_READ,
                IOobject::AUTO_WRITE
            ),
            sqrt(8.0*permeability/(eps + dimensionedScalar("small", dimless, SMALL)))
        );

        // Calculate local Reynolds number: Re = |U|*L_pore/nu
        volScalarField Re_pore
        (
            IOobject
            (
                "Re_pore",
                runTime.timeName(),
                mesh,
                IOobject::NO_READ,
                IOobject::AUTO_WRITE
            ),
            mag(U)*L_pore/(nu + dimensionedScalar("small", dimViscosity, SMALL))
        );

        // Calculate Darcy velocity magnitude
        volScalarField U_Darcy
        (
            IOobject
            (
                "U_Darcy",
                runTime.timeName(),
                mesh,
                IOobject::NO_READ,
                IOobject::AUTO_WRITE
            ),
            mag(U)*eps
        );

        // Write fields
        permeability.write();
        L_pore.write();
        Re_pore.write();
        U_Darcy.write();

        Info<< "    Permeability statistics:" << endl
            << "        Min: " << gMin(permeability) << " m²" << endl
            << "        Max: " << gMax(permeability) << " m²" << endl
            << "        Average: " << permeability.weightedAverage(mesh.V()).value() << " m²" << endl;

        Info<< "    Pore Reynolds number statistics:" << endl
            << "        Min: " << gMin(Re_pore) << endl
            << "        Max: " << gMax(Re_pore) << endl
            << "        Average: " << Re_pore.weightedAverage(mesh.V()).value() << endl;

        Info<< endl;
    }

    Info<< "End\n" << endl;

    return 0;
}

// ************************************************************************* //

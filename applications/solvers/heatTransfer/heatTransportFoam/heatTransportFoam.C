/*---------------------------------------------------------------------------*\
License
    This file is part of GeoChemFoam, an Open source software using OpenFOAM
    for multiphase multicomponent reactive transport simulation in pore-scale
    geological domain.

    GeoChemFoam is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by the
    Free Software Foundation, either version 3 of the License, or (at your
    option) any later version. See <http://www.gnu.org/licenses/>.

    The code was developed by <PERSON> as part of his research work for
    the GeoChemFoam Group at Heriot-Watt University. Please visit our
    website for more information <https://github.com/GeoChemFoam>.

Application
    heatTransportSimpleFoam

Description
    Solves a simple temperature equation, with convection and diffusion in the fluid and diffusion in the solid 

\*---------------------------------------------------------------------------*/

#include "fvCFD.H"
#include "fvOptions.H"
#include "simpleControl.H"

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

int main(int argc, char *argv[])
{
    argList::addNote
    (
        "Solves a simple temperature equation, with convection and diffusion in the fluid and diffusion in the solid"
    );

    #include "postProcess.H"

    #include "addCheckCaseOptions.H"
    #include "setRootCaseLists.H"
    #include "createTime.H"
    #include "createMesh.H"

    simpleControl simple(mesh);

    #include "createFields.H"

    #include "CourantNo.H"


    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

    Info<< "\nCalculating temperature distribution\n" << endl;
    volScalarField c= eps+(1-eps)*cs;
    surfaceScalarField D= fvc::interpolate(eps*DTf+(1-eps)*DTs);
    if (diffusionModel=="arithmetic")
    {
        //nothing to do
    }
    else if (diffusionModel=="harmonic")
    {
        D = DTf*DTs/fvc::interpolate(eps*DTs + (1-eps)*DTf);
    }
    else
    {
        Info << "Error: only arithmetic and harmonic diffusion model implemented" 
        << endl
        << abort(FatalError);
    }

    while (simple.loop())
    {
        Info<< "Time = " << runTime.timeName() << nl << endl;

        while (simple.correctNonOrthogonal())
        {
            fvScalarMatrix TEqn
            (
                fvm::ddt(c,T) + fvm::div(phi,T) + fvm::SuSp(-fvc::div(phi),T) - fvm::laplacian(D, T)
             ==
                fvOptions(T)
            );

            fvOptions.constrain(TEqn);
            TEqn.relax();
            TEqn.solve();
            fvOptions.correct(T);
        }

        #include "write.H"

        runTime.printExecutionTime(Info);
    }

    Info<< "End\n" << endl;

    return 0;
}


// ************************************************************************* //

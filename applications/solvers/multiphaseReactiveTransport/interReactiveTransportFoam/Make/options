EXE_INC = \
    -I../../multiphase/VoF \
    -I$(LIB_SRC)/finiteVolume/lnInclude \
    -I$(LIB_SRC)/meshTools/lnInclude \
    -I$(LIB_SRC)/sampling/lnInclude \
    -I$(LIB_SRC)/dynamicFvMesh/lnInclude \
    -I$(GCFOAM_SRC)/thermophysicalModels/reactionThermo/lnInclude \
    -I$(LIB_SRC)/thermophysicalModels/specie/lnInclude \
    -I$(LIB_SRC)/transportModels \
    -I$(LIB_SRC)/transportModels/incompressible/lnInclude \
    -I$(GCFOAM_SRC)/transportModels/interfaceProperties/lnInclude \
    -I$(LIB_SRC)/transportModels/twoPhaseMixture/lnInclude \
    -I$(LIB_SRC)/TurbulenceModels/turbulenceModels/lnInclude \
    -I$(LIB_SRC)/TurbulenceModels/incompressible/lnInclude \
    -I$(GCFOAM_SRC)/transportModels/immiscibleIncompressibleTwoPhaseMixture/lnInclude

EXE_LIBS = \
    -L$(GCFOAM_LIBBIN) \
    -lfiniteVolume \
    -lfvOptions \
    -lmeshTools \
    -lsampling \
    -ldynamicFvMesh \
    -lincompressibleTransportModels \
    -lreactionThermophysicalModelsGCFOAM \
    -lspecie \
    -linterfacePropertiesGCFOAM \
    -limmiscibleIncompressibleTwoPhaseMixtureGCFOAM \
    -lturbulenceModels \
    -lincompressibleTurbulenceModels \
    -lwaveModels

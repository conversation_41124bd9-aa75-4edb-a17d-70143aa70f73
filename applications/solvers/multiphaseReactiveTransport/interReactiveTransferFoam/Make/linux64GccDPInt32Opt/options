# 0 "Make/options"
# 0 "<built-in>"
# 0 "<command-line>"






# 1 "/usr/include/stdc-predef.h" 1 3 4

# 17 "/usr/include/stdc-predef.h" 3 4



















# 45 "/usr/include/stdc-predef.h" 3 4

# 55 "/usr/include/stdc-predef.h" 3 4









# 6 "<command-line>" 2
# 1 "Make/options"
EXE_INC = -I../../multiphase/VoF -I$(LIB_SRC)/finiteVolume/lnInclude -I$(LIB_SRC)/meshTools/lnInclude -I$(LIB_SRC)/sampling/lnInclude -I$(LIB_SRC)/dynamicFvMesh/lnInclude -I$(GCFOAM_SRC)/thermophysicalModels/reactionThermo/lnInclude -I$(LIB_SRC)/thermophysicalModels/specie/lnInclude -I$(LIB_SRC)/transportModels -I$(LIB_SRC)/transportModels/incompressible/lnInclude -I$(GCFOAM_SRC)/transportModels/interfaceProperties/lnInclude -I$(LIB_SRC)/transportModels/twoPhaseMixture/lnInclude -I$(LIB_SRC)/TurbulenceModels/turbulenceModels/lnInclude -I$(LIB_SRC)/TurbulenceModels/incompressible/lnInclude -I$(GCFOAM_SRC)/transportModels/immiscibleIncompressibleTwoPhaseMixture/lnInclude
# 16 "Make/options"

EXE_LIBS = -L$(GCFOAM_LIBBIN) -lfiniteVolume -lfvOptions -lmeshTools -lsampling -ldynamicFvMesh -lincompressibleTransportModels -lreactionThermophysicalModelsGCFOAM -lspecie -linterfacePropertiesGCFOAM -limmiscibleIncompressibleTwoPhaseMixtureGCFOAM -lturbulenceModels -lincompressibleTurbulenceModels -lwaveModels

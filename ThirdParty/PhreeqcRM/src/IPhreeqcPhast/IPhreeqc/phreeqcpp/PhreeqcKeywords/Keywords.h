#ifndef _INC_KEYWORDS_H
#define _INC_KEYWORDS_H
#include <string>
#include <map>
class Keywords
{
public:
		enum KEYWORDS
	{
		KEY_NONE,
		KEY_E<PERSON>,
		<PERSON>EY_SOLUTION_SPECIES,
		<PERSON><PERSON><PERSON>_SOLUTION_MASTER_SPECIES,
		<PERSON><PERSON><PERSON>_SOLUTION,
		<PERSON><PERSON><PERSON>_PHASES,
		KEY_REACTION,
		KEY_MIX,
		KEY_USE,
		KEY_SAVE,
		<PERSON>EY_EXCHANGE_SPECIES,
		KEY_EXCHANGE_MASTER_SPECIES,
		KEY_EXCHANGE,
		KEY_SURFACE_SPECIES,
		KEY_SURFACE_MASTER_SPECIES,
		KEY_SURFACE,
		KEY_REACTION_TEMPERATURE,
		KEY_INVERSE_MODELING,
		KEY_GAS_PHASE,
		KEY_TRANSPORT,
		KEY_SELECTED_OUTPUT,
		<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON>_PRIN<PERSON>,
		<PERSON><PERSON><PERSON>_EQUILIBRIUM_PHASES,
		<PERSON><PERSON><PERSON>_TITLE,
		<PERSON><PERSON><PERSON>_ADVECTION,
		<PERSON><PERSON><PERSON>_KINETIC<PERSON>,
		<PERSON><PERSON><PERSON>_INCREMENTAL_REACTIONS,
		<PERSON><PERSON><PERSON>_RATES,
		KEY_USER_PRINT,
		KEY_USER_PUNCH,
		KEY_SOLID_SOLUTIONS,
		KEY_SOLUTION_SPREAD,
		KEY_USER_GRAPH,
		KEY_LLNL_AQUEOUS_MODEL_PARAMETERS,
		KEY_DATABASE,
		KEY_NAMED_EXPRESSIONS,
		KEY_ISOTOPES,
		KEY_CALCULATE_VALUES,
		KEY_ISOTOPE_RATIOS,
		KEY_ISOTOPE_ALPHAS,
		KEY_COPY,
		KEY_PITZER,
		KEY_SIT,
		KEY_SOLUTION_RAW,
		KEY_EXCHANGE_RAW,
		KEY_SURFACE_RAW,
		KEY_EQUILIBRIUM_PHASES_RAW,
		KEY_KINETICS_RAW,
		KEY_SOLID_SOLUTIONS_RAW,
		KEY_GAS_PHASE_RAW,
		KEY_REACTION_RAW,
		KEY_MIX_RAW,
		KEY_REACTION_TEMPERATURE_RAW,
		KEY_DUMP,
		KEY_SOLUTION_MODIFY,
		KEY_EQUILIBRIUM_PHASES_MODIFY,
		KEY_EXCHANGE_MODIFY,
		KEY_SURFACE_MODIFY,
		KEY_SOLID_SOLUTIONS_MODIFY,
		KEY_GAS_PHASE_MODIFY,
		KEY_KINETICS_MODIFY,
		KEY_DELETE,
		KEY_RUN_CELLS,
		KEY_REACTION_MODIFY,
		KEY_REACTION_TEMPERATURE_MODIFY,
		KEY_REACTION_PRESSURE,
		KEY_REACTION_PRESSURE_RAW,
		KEY_REACTION_PRESSURE_MODIFY,
		KEY_SOLUTION_MIX,
		KEY_EXCHANGE_MIX,
		KEY_GAS_PHASE_MIX,
		KEY_KINETICS_MIX,
		KEY_PPASSEMBLAGE_MIX,
		KEY_SSASSEMBLAGE_MIX,
		KEY_SURFACE_MIX,
		KEY_COUNT_KEYWORDS // must be last in list
	};

	Keywords(void);
	~Keywords(void);

	static KEYWORDS Keyword_search(std::string key);
	static const std::string & Keyword_name_search(KEYWORDS key);

	static const std::map<const std::string, KEYWORDS> phreeqc_keywords;
	static const std::map<KEYWORDS, const std::string> phreeqc_keyword_names;
};

#endif // _INC_KEYWORDS_H
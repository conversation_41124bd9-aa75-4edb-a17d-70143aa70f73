SOURCE = \
IPhreeqcPhast/IPhreeqc/phreeqcpp/common/Parser.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/common/PHRQ_base.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/common/PHRQ_io.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/common/Utils.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/PhreeqcKeywords/Keywords.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/advection.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/basicsubs.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/ChartHandler.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/ChartObject.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/cl1.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/CurveObject.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/cvdense.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/cvode.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/cxxKinetics.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/cxxMix.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/dense.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Dictionary.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/dumper.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Exchange.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/ExchComp.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/GasComp.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/gases.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/GasPhase.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/input.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/integrate.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/inverse.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/ISolution.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/ISolutionComp.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/isotopes.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/kinetics.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/KineticsComp.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/mainsubs.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/model.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/NameDouble.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/NumKeyword.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/nvector.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/nvector_serial.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/parse.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/PBasic.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/phqalloc.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Phreeqc.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/PHRQ_io_output.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/pitzer.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/pitzer_structures.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/PPassemblage.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/PPassemblageComp.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/prep.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Pressure.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/print.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Reaction.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/read.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/ReadClass.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/readtr.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/runner.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/SelectedOutput.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Serializer.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/sit.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/smalldense.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Solution.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/SolutionIsotope.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/spread.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/SS.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/SSassemblage.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/SScomp.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/step.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/StorageBin.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/StorageBinList.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/structures.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/sundialsmath.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Surface.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/SurfaceCharge.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/SurfaceComp.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/System.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/tally.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Temperature.cxx \
IPhreeqcPhast/IPhreeqc/phreeqcpp/tidy.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/transport.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/Use.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/UserPunch.cpp \
IPhreeqcPhast/IPhreeqc/phreeqcpp/utilities.cpp \
IPhreeqcPhast/IPhreeqc/CSelectedOutput.cpp \
IPhreeqcPhast/IPhreeqc/IPhreeqc.cpp \
IPhreeqcPhast/IPhreeqc/IPhreeqc_interface_F.cpp \
IPhreeqcPhast/IPhreeqc/IPhreeqcLib.cpp \
IPhreeqcPhast/IPhreeqc/Var.c \
IPhreeqcPhast/IPhreeqcPhast.cxx \
IPhreeqcPhast/IPhreeqcPhastLib.cpp \
PhreeqcRM.cpp \
RM_interface_C.cpp \
RM_interface_F.cpp \

# sources

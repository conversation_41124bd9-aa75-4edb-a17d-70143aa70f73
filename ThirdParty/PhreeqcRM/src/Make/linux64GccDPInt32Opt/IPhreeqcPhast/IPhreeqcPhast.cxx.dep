$(OBJECTS_DIR)/IPhreeqcPhast/IPhreeqcPhast.cxx.dep: \
IPhreeqcPhast/IPhreeqcPhast.cxx \
IPhreeqcPhast/IPhreeqcPhast.h \
lnInclude/PHRQ_base.h \
lnInclude/StorageBin.h \
lnInclude/System.h \
lnInclude/NameDouble.h \
lnInclude/Parser.h \
lnInclude/Keywords.h \
lnInclude/PHRQ_io.h \
lnInclude/phrqtype.h \
lnInclude/IPhreeqc.hpp \
lnInclude/IPhreeqcCallbacks.h \
lnInclude/Var.h \
lnInclude/Phreeqc.h \
lnInclude/cvdense.h \
lnInclude/cvode.h \
lnInclude/sundialstypes.h \
lnInclude/nvector.h \
lnInclude/dense.h \
lnInclude/smalldense.h \
lnInclude/runner.h \
lnInclude/StorageBinList.h \
lnInclude/dumper.h \
lnInclude/SelectedOutput.h \
lnInclude/NumKeyword.h \
lnInclude/UserPunch.h \
lnInclude/ChartHandler.h \
lnInclude/ChartObject.h \
lnInclude/CurveObject.h \
lnInclude/Pressure.h \
lnInclude/cxxMix.h \
lnInclude/Use.h \
lnInclude/Surface.h \
lnInclude/SurfaceComp.h \
lnInclude/SurfaceCharge.h \
lnInclude/thread.h \
lnInclude/global_structures.h \
lnInclude/NA.h \
lnInclude/Solution.h \
lnInclude/SolutionIsotope.h \
lnInclude/ISolution.h \
lnInclude/ISolutionComp.h \
lnInclude/GasPhase.h \
lnInclude/GasComp.h \
lnInclude/CSelectedOutput.hxx \
lnInclude/CVar.hxx \
lnInclude/cxxKinetics.h \
lnInclude/KineticsComp.h \

#END

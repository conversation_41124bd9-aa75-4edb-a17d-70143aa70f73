$(OBJECTS_DIR)/IPhreeqcPhast/IPhreeqc/IPhreeqc.cpp.dep: \
IPhreeqcPhast/IPhreeqc/IPhreeqc.cpp \
IPhreeqcPhast/IPhreeqc/IPhreeqc.hpp \
IPhreeqcPhast/IPhreeqc/IPhreeqcCallbacks.h \
IPhreeqcPhast/IPhreeqc/Var.h \
lnInclude/PHRQ_io.h \
lnInclude/Keywords.h \
lnInclude/Phreeqc.h \
lnInclude/phrqtype.h \
lnInclude/cvdense.h \
lnInclude/cvode.h \
lnInclude/sundialstypes.h \
lnInclude/nvector.h \
lnInclude/dense.h \
lnInclude/smalldense.h \
lnInclude/runner.h \
lnInclude/StorageBinList.h \
lnInclude/PHRQ_base.h \
lnInclude/dumper.h \
lnInclude/SelectedOutput.h \
lnInclude/NumKeyword.h \
lnInclude/UserPunch.h \
lnInclude/ChartHandler.h \
lnInclude/Parser.h \
lnInclude/ChartObject.h \
lnInclude/CurveObject.h \
lnInclude/Pressure.h \
lnInclude/cxxMix.h \
lnInclude/Use.h \
lnInclude/Surface.h \
lnInclude/SurfaceComp.h \
lnInclude/NameDouble.h \
lnInclude/SurfaceCharge.h \
IPhreeqcPhast/IPhreeqc/thread.h \
lnInclude/global_structures.h \
lnInclude/NA.h \
IPhreeqcPhast/IPhreeqc/Version.h \
IPhreeqcPhast/IPhreeqc/Debug.h \
IPhreeqcPhast/IPhreeqc/ErrorReporter.hxx \
IPhreeqcPhast/IPhreeqc/CSelectedOutput.hxx \
IPhreeqcPhast/IPhreeqc/CVar.hxx \

#END

#!/bin/bash

#######################################################################
# 3D Calcite Post 渗透率分析自动化脚本
# 
# 该脚本自动执行以下步骤:
# 1. 检查算例目录和必要文件
# 2. 运行 processPoroPerm 工具计算渗透率
# 3. 使用 Python 脚本生成 Paraview 可视化文件
# 4. 生成分析报告和图表
#
# 使用方法: ./run_permeability_analysis.sh <case_directory>
#
# 作者: AI Assistant
# 日期: 2025-07-31
#######################################################################

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -ne 1 ]; then
    print_error "使用方法: $0 <case_directory>"
    print_info "示例: $0 tutorials/reactiveTransport/reactiveTransportDBSFoam/3DcalcitePostDBS"
    exit 1
fi

CASE_DIR="$1"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 检查算例目录
if [ ! -d "$CASE_DIR" ]; then
    print_error "算例目录不存在: $CASE_DIR"
    exit 1
fi

print_info "开始分析算例: $CASE_DIR"

# 进入算例目录
cd "$CASE_DIR"
CASE_ABS_PATH="$(pwd)"

print_info "算例绝对路径: $CASE_ABS_PATH"

# 检查必要文件
print_info "检查必要文件..."

if [ ! -f "system/postProcessDict" ]; then
    print_warning "postProcessDict 不存在，创建默认配置"
    cat > system/postProcessDict << EOF
FoamFile
{
version     2.0;
format      ascii;
class       dictionary;
location    "system";
object      postProcessDict;
}

x1 0;
y1 0;
z1 0;

x2 .002680;
y2 .001500;
z2 .000200;

direction 0;

velocity volumeAveraged;
pressureDrop direct;
EOF
fi

if [ ! -f "constant/transportProperties" ]; then
    print_error "transportProperties 文件不存在"
    exit 1
fi

# 检查时间目录
TIME_DIRS=$(find . -maxdepth 1 -type d -name "[0-9]*" | wc -l)
if [ "$TIME_DIRS" -eq 0 ]; then
    print_error "未找到任何时间目录，请确保算例已经运行完成"
    exit 1
fi

print_success "找到 $TIME_DIRS 个时间目录"

# 检查是否存在并行计算结果
if [ -d "processor0" ]; then
    print_info "检测到并行计算结果"
    PARALLEL=true
    NP=$(find processor* -maxdepth 0 -type d -print | wc -l)
    print_info "处理器数量: $NP"
else
    print_info "检测到串行计算结果"
    PARALLEL=false
fi

# 运行 processPoroPerm
print_info "运行 processPoroPerm 计算渗透率..."

if [ "$PARALLEL" = true ]; then
    print_info "并行模式运行 processPoroPerm"
    if command -v mpiexec >/dev/null 2>&1; then
        mpiexec -np $NP processPoroPerm -parallel > processPoroPerm.log 2>&1
    else
        print_error "未找到 mpiexec，无法运行并行版本"
        exit 1
    fi
else
    print_info "串行模式运行 processPoroPerm"
    processPoroPerm > processPoroPerm.log 2>&1
fi

# 检查 processPoroPerm 是否成功
if [ $? -eq 0 ] && [ -f "poroPerm.csv" ]; then
    print_success "processPoroPerm 运行成功"
    print_info "生成文件: poroPerm.csv"
    
    # 显示前几行结果
    print_info "渗透率计算结果预览:"
    head -5 poroPerm.csv
else
    print_error "processPoroPerm 运行失败，请检查日志文件: processPoroPerm.log"
    exit 1
fi

# 运行 Python 脚本生成 Paraview 文件
print_info "生成 Paraview 可视化文件..."

PYTHON_SCRIPT="$SCRIPT_DIR/process_permeability_paraview.py"

if [ ! -f "$PYTHON_SCRIPT" ]; then
    print_error "Python 脚本不存在: $PYTHON_SCRIPT"
    exit 1
fi

# 检查 Python 和必要的库
if ! command -v python3 >/dev/null 2>&1; then
    print_error "未找到 python3"
    exit 1
fi

# 检查 pandas
if ! python3 -c "import pandas" >/dev/null 2>&1; then
    print_warning "pandas 未安装，尝试安装..."
    pip3 install pandas --user
fi

# 运行 Python 脚本
python3 "$PYTHON_SCRIPT" "$CASE_ABS_PATH" -o permeability_results

if [ $? -eq 0 ]; then
    print_success "Paraview 文件生成成功"
else
    print_error "Paraview 文件生成失败"
    exit 1
fi

# 生成使用说明
print_info "生成使用说明文件..."

cat > permeability_results/README.md << EOF
# 3D Calcite Post 渗透率分析结果

## 文件说明

- \`permeability_case/\`: Paraview 可视化算例目录
- \`permeability_case/permeability.foam\`: Paraview 加载文件
- \`permeability_analysis.png\`: 渗透率分析图表
- \`permeability_report.txt\`: 详细分析报告
- \`poroPerm.csv\`: 原始渗透率数据 (来自 processPoroPerm)

## 使用方法

### 1. 在 Paraview 中查看结果

1. 打开 Paraview
2. 加载文件: \`permeability_case/permeability.foam\`
3. 在 Properties 面板中点击 Apply
4. 选择要显示的场变量:
   - \`eps\`: 孔隙率场
   - \`permeability\`: 渗透率场
   - \`porosity\`: 孔隙率 (uniform 值)
   - \`U\`: 速度场
   - \`p\`: 压力场

### 2. 时间序列动画

1. 在 Paraview 中加载数据后
2. 使用底部的时间控制器播放动画
3. 观察渗透率随时间的变化

### 3. 数据分析

- 查看 \`permeability_analysis.png\` 了解整体趋势
- 阅读 \`permeability_report.txt\` 获取详细统计信息
- 使用 \`poroPerm.csv\` 进行进一步的数据分析

## 计算参数

- 计算域: 根据 system/postProcessDict 中的设置
- 流动方向: x 方向 (direction = 0)
- 渗透率计算公式: k = U × ν / (dp/dx)

其中:
- U: 平均速度
- ν: 动力粘度
- dp/dx: 压力梯度

## 注意事项

1. 渗透率计算基于达西定律
2. 假设流动为层流
3. 使用体积平均的孔隙率和速度
4. 压力梯度通过直接计算获得

EOF

# 显示结果摘要
print_success "分析完成!"
echo ""
print_info "结果文件位置:"
echo "  - Paraview 文件: $CASE_ABS_PATH/permeability_results/permeability_case/"
echo "  - 分析图表: $CASE_ABS_PATH/permeability_results/permeability_analysis.png"
echo "  - 分析报告: $CASE_ABS_PATH/permeability_results/permeability_report.txt"
echo "  - 使用说明: $CASE_ABS_PATH/permeability_results/README.md"
echo ""

print_info "在 Paraview 中打开:"
echo "  paraview $CASE_ABS_PATH/permeability_results/permeability_case/permeability.foam"
echo ""

# 显示一些统计信息
if [ -f "poroPerm.csv" ]; then
    print_info "渗透率分析摘要:"
    
    # 使用 awk 计算统计信息
    FIRST_LINE=$(sed -n '2p' poroPerm.csv)
    LAST_LINE=$(tail -n 1 poroPerm.csv)
    
    if [ -n "$FIRST_LINE" ] && [ -n "$LAST_LINE" ]; then
        INITIAL_PORO=$(echo $FIRST_LINE | awk '{print $2}')
        FINAL_PORO=$(echo $LAST_LINE | awk '{print $2}')
        INITIAL_PERM=$(echo $FIRST_LINE | awk '{print $3}')
        FINAL_PERM=$(echo $LAST_LINE | awk '{print $3}')
        
        echo "  初始孔隙率: $INITIAL_PORO"
        echo "  最终孔隙率: $FINAL_PORO"
        echo "  初始渗透率: $INITIAL_PERM m²"
        echo "  最终渗透率: $FINAL_PERM m²"
    fi
fi

print_success "渗透率分析流程全部完成!"

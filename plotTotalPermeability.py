#!/usr/bin/env python3
"""
绘制总渗透率时间演化图

该脚本读取totalPermeability.csv文件并生成可视化图表

作者: AI Assistant
日期: 2025-07-31
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import sys
import os

def plot_total_permeability(csv_file="totalPermeability.csv", output_dir="."):
    """绘制总渗透率分析图表"""
    
    if not os.path.exists(csv_file):
        print(f"错误: 文件 {csv_file} 不存在")
        print("请先运行 calculateTotalPermeability 工具")
        return False
        
    try:
        # 读取数据
        df = pd.read_csv(csv_file)
        
        print(f"读取了 {len(df)} 个时间步的数据")
        print(f"时间范围: {df['time'].min()} - {df['time'].max()}")
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('Total Permeability Analysis - 3D Calcite Post', fontsize=16, fontweight='bold')
        
        # 1. 孔隙率随时间变化
        axes[0, 0].plot(df['time'], df['porosity'], 'b-o', linewidth=2, markersize=6)
        axes[0, 0].set_xlabel('Time')
        axes[0, 0].set_ylabel('Porosity')
        axes[0, 0].set_title('Porosity Evolution')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].set_ylim([df['porosity'].min()*0.99, df['porosity'].max()*1.01])
        
        # 2. 总渗透率随时间变化
        # 过滤掉零值
        df_nonzero = df[df['totalPermeability'] > 0]
        if len(df_nonzero) > 0:
            axes[0, 1].semilogy(df_nonzero['time'], df_nonzero['totalPermeability'], 'r-o', linewidth=2, markersize=6)
            axes[0, 1].set_xlabel('Time')
            axes[0, 1].set_ylabel('Total Permeability (m²)')
            axes[0, 1].set_title('Total Permeability Evolution')
            axes[0, 1].grid(True, alpha=0.3)
        else:
            axes[0, 1].text(0.5, 0.5, 'No non-zero permeability data', 
                           ha='center', va='center', transform=axes[0, 1].transAxes)
            axes[0, 1].set_title('Total Permeability Evolution')
        
        # 3. 流量随时间变化
        df_flow = df[df['flowRate'] > 0]
        if len(df_flow) > 0:
            axes[0, 2].plot(df_flow['time'], df_flow['flowRate'], 'g-o', linewidth=2, markersize=6)
            axes[0, 2].set_xlabel('Time')
            axes[0, 2].set_ylabel('Flow Rate (m³/s)')
            axes[0, 2].set_title('Flow Rate Evolution')
            axes[0, 2].grid(True, alpha=0.3)
            axes[0, 2].ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
        else:
            axes[0, 2].text(0.5, 0.5, 'No flow data', 
                           ha='center', va='center', transform=axes[0, 2].transAxes)
            axes[0, 2].set_title('Flow Rate Evolution')
        
        # 4. 压力降随时间变化
        df_pressure = df[df['pressureDrop'] > 0]
        if len(df_pressure) > 0:
            axes[1, 0].plot(df_pressure['time'], df_pressure['pressureDrop'], 'm-o', linewidth=2, markersize=6)
            axes[1, 0].set_xlabel('Time')
            axes[1, 0].set_ylabel('Pressure Drop (Pa)')
            axes[1, 0].set_title('Pressure Drop Evolution')
            axes[1, 0].grid(True, alpha=0.3)
        else:
            axes[1, 0].text(0.5, 0.5, 'No pressure drop data', 
                           ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('Pressure Drop Evolution')
        
        # 5. 达西速度随时间变化
        df_darcy = df[df['DarcyVelocity'] > 0]
        if len(df_darcy) > 0:
            axes[1, 1].plot(df_darcy['time'], df_darcy['DarcyVelocity'], 'c-o', linewidth=2, markersize=6)
            axes[1, 1].set_xlabel('Time')
            axes[1, 1].set_ylabel('Darcy Velocity (m/s)')
            axes[1, 1].set_title('Darcy Velocity Evolution')
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, 'No Darcy velocity data', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Darcy Velocity Evolution')
        
        # 6. 雷诺数随时间变化
        df_re = df[df['Reynolds'] > 0]
        if len(df_re) > 0:
            axes[1, 2].plot(df_re['time'], df_re['Reynolds'], 'orange', marker='o', linewidth=2, markersize=6)
            axes[1, 2].set_xlabel('Time')
            axes[1, 2].set_ylabel('Reynolds Number')
            axes[1, 2].set_title('Reynolds Number Evolution')
            axes[1, 2].grid(True, alpha=0.3)
            
            # 添加层流/湍流分界线
            axes[1, 2].axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Laminar/Turbulent')
            axes[1, 2].legend()
        else:
            axes[1, 2].text(0.5, 0.5, 'No Reynolds number data', 
                           ha='center', va='center', transform=axes[1, 2].transAxes)
            axes[1, 2].set_title('Reynolds Number Evolution')
        
        plt.tight_layout()
        
        # 保存图表
        output_file = os.path.join(output_dir, "totalPermeability_analysis.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"图表已保存: {output_file}")
        
        # 显示统计信息
        print("\n=== 总渗透率分析统计 ===")
        if len(df_nonzero) > 0:
            print(f"渗透率范围: {df_nonzero['totalPermeability'].min():.2e} - {df_nonzero['totalPermeability'].max():.2e} m²")
            if len(df_nonzero) > 1:
                perm_change = (df_nonzero['totalPermeability'].iloc[-1] / df_nonzero['totalPermeability'].iloc[0] - 1) * 100
                print(f"渗透率变化: {perm_change:.1f}%")
        
        poro_change = (df['porosity'].iloc[-1] - df['porosity'].iloc[0]) * 100
        print(f"孔隙率变化: {poro_change:.2f} 百分点")
        
        if len(df_re) > 0:
            print(f"雷诺数范围: {df_re['Reynolds'].min():.3f} - {df_re['Reynolds'].max():.3f}")
            if df_re['Reynolds'].max() < 1:
                print("流动状态: 层流 (Re < 1)")
            else:
                print("流动状态: 可能存在湍流 (Re ≥ 1)")
        
        plt.show()
        return True
        
    except Exception as e:
        print(f"处理数据时出错: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    else:
        csv_file = "totalPermeability.csv"
        
    if len(sys.argv) > 2:
        output_dir = sys.argv[2]
    else:
        output_dir = "."
        
    print("总渗透率可视化工具")
    print("=" * 30)
    
    success = plot_total_permeability(csv_file, output_dir)
    
    if success:
        print("\n可视化完成！")
    else:
        print("\n可视化失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
